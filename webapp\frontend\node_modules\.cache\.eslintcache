[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "81", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "93", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "95", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "97", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "100", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "103", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "104", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "105", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "106"}, {"size": 557, "mtime": 1746952718482, "results": "107", "hashOfConfig": "108"}, {"size": 2728, "mtime": 1748879437279, "results": "109", "hashOfConfig": "108"}, {"size": 996, "mtime": 1746970152489, "results": "110", "hashOfConfig": "108"}, {"size": 10788, "mtime": 1746864244183, "results": "111", "hashOfConfig": "108"}, {"size": 21191, "mtime": 1748751093271, "results": "112", "hashOfConfig": "108"}, {"size": 7269, "mtime": 1748879407880, "results": "113", "hashOfConfig": "108"}, {"size": 2216, "mtime": 1746640055487, "results": "114", "hashOfConfig": "108"}, {"size": 7394, "mtime": 1748034003517, "results": "115", "hashOfConfig": "108"}, {"size": 6749, "mtime": 1746282201800, "results": "116", "hashOfConfig": "108"}, {"size": 18812, "mtime": 1748879486791, "results": "117", "hashOfConfig": "108"}, {"size": 2535, "mtime": 1746647873596, "results": "118", "hashOfConfig": "108"}, {"size": 2050, "mtime": 1746647945415, "results": "119", "hashOfConfig": "108"}, {"size": 700, "mtime": 1747545501078, "results": "120", "hashOfConfig": "108"}, {"size": 17518, "mtime": 1748664526035, "results": "121", "hashOfConfig": "108"}, {"size": 3028, "mtime": 1748816305304, "results": "122", "hashOfConfig": "108"}, {"size": 2070, "mtime": 1748815989656, "results": "123", "hashOfConfig": "108"}, {"size": 1630, "mtime": 1746336079554, "results": "124", "hashOfConfig": "108"}, {"size": 1909, "mtime": 1748722592098, "results": "125", "hashOfConfig": "108"}, {"size": 43832, "mtime": 1748878207301, "results": "126", "hashOfConfig": "108"}, {"size": 324, "mtime": 1748757444974, "results": "127", "hashOfConfig": "108"}, {"size": 9068, "mtime": 1746856425683, "results": "128", "hashOfConfig": "108"}, {"size": 2210, "mtime": 1747432283057, "results": "129", "hashOfConfig": "108"}, {"size": 4494, "mtime": 1748121063631, "results": "130", "hashOfConfig": "108"}, {"size": 38195, "mtime": 1748813903832, "results": "131", "hashOfConfig": "108"}, {"size": 3337, "mtime": 1748816346924, "results": "132", "hashOfConfig": "108"}, {"size": 2958, "mtime": 1748816316425, "results": "133", "hashOfConfig": "108"}, {"size": 3507, "mtime": 1748816326922, "results": "134", "hashOfConfig": "108"}, {"size": 3345, "mtime": 1748816357091, "results": "135", "hashOfConfig": "108"}, {"size": 3340, "mtime": 1748816336281, "results": "136", "hashOfConfig": "108"}, {"size": 2975, "mtime": 1747554796402, "results": "137", "hashOfConfig": "108"}, {"size": 3429, "mtime": 1747721794176, "results": "138", "hashOfConfig": "108"}, {"size": 3109, "mtime": 1747824114392, "results": "139", "hashOfConfig": "108"}, {"size": 2929, "mtime": 1747655572696, "results": "140", "hashOfConfig": "108"}, {"size": 3302, "mtime": 1748000902435, "results": "141", "hashOfConfig": "108"}, {"size": 6125, "mtime": 1748705680231, "results": "142", "hashOfConfig": "108"}, {"size": 5880, "mtime": 1748121404574, "results": "143", "hashOfConfig": "108"}, {"size": 3889, "mtime": 1748664890350, "results": "144", "hashOfConfig": "108"}, {"size": 4720, "mtime": 1746771178920, "results": "145", "hashOfConfig": "108"}, {"size": 7121, "mtime": 1746281148395, "results": "146", "hashOfConfig": "108"}, {"size": 7958, "mtime": 1746280443400, "results": "147", "hashOfConfig": "108"}, {"size": 6259, "mtime": 1746965906057, "results": "148", "hashOfConfig": "108"}, {"size": 4215, "mtime": 1746278746358, "results": "149", "hashOfConfig": "108"}, {"size": 1273, "mtime": 1746809069006, "results": "150", "hashOfConfig": "108"}, {"size": 14270, "mtime": 1748371983481, "results": "151", "hashOfConfig": "108"}, {"size": 2752, "mtime": 1747022186740, "results": "152", "hashOfConfig": "108"}, {"size": 1072, "mtime": 1746637929350, "results": "153", "hashOfConfig": "108"}, {"size": 6745, "mtime": 1747545492454, "results": "154", "hashOfConfig": "108"}, {"size": 41680, "mtime": 1748816669877, "results": "155", "hashOfConfig": "108"}, {"size": 500, "mtime": 1748722841235, "results": "156", "hashOfConfig": "108"}, {"size": 47844, "mtime": 1748876421138, "results": "157", "hashOfConfig": "108"}, {"size": 38669, "mtime": 1748199713253, "results": "158", "hashOfConfig": "108"}, {"size": 1947, "mtime": 1748120984640, "results": "159", "hashOfConfig": "108"}, {"size": 54895, "mtime": 1748370360136, "results": "160", "hashOfConfig": "108"}, {"size": 14635, "mtime": 1748666301849, "results": "161", "hashOfConfig": "108"}, {"size": 5700, "mtime": 1748878577115, "results": "162", "hashOfConfig": "108"}, {"size": 11771, "mtime": 1746948731812, "results": "163", "hashOfConfig": "108"}, {"size": 2211, "mtime": 1748686293878, "results": "164", "hashOfConfig": "108"}, {"size": 9215, "mtime": 1748668814050, "results": "165", "hashOfConfig": "108"}, {"size": 10993, "mtime": 1747154871546, "results": "166", "hashOfConfig": "108"}, {"size": 12150, "mtime": 1748205557322, "results": "167", "hashOfConfig": "108"}, {"size": 24566, "mtime": 1748691444876, "results": "168", "hashOfConfig": "108"}, {"size": 7032, "mtime": 1748069273238, "results": "169", "hashOfConfig": "108"}, {"size": 8589, "mtime": 1748207111023, "results": "170", "hashOfConfig": "108"}, {"size": 9979, "mtime": 1748069243848, "results": "171", "hashOfConfig": "108"}, {"size": 10821, "mtime": 1748069202177, "results": "172", "hashOfConfig": "108"}, {"size": 36555, "mtime": 1747684003188, "results": "173", "hashOfConfig": "108"}, {"size": 9483, "mtime": 1747194869458, "results": "174", "hashOfConfig": "108"}, {"size": 16178, "mtime": 1748875708468, "results": "175", "hashOfConfig": "108"}, {"size": 48588, "mtime": 1747948123233, "results": "176", "hashOfConfig": "108"}, {"size": 92270, "mtime": 1748123070273, "results": "177", "hashOfConfig": "108"}, {"size": 522, "mtime": 1747022186711, "results": "178", "hashOfConfig": "108"}, {"size": 10251, "mtime": 1748805459799, "results": "179", "hashOfConfig": "108"}, {"size": 7740, "mtime": 1748881233022, "results": "180", "hashOfConfig": "108"}, {"size": 1703, "mtime": 1746972529152, "results": "181", "hashOfConfig": "108"}, {"size": 19892, "mtime": 1747554544219, "results": "182", "hashOfConfig": "108"}, {"size": 12050, "mtime": 1747547543421, "results": "183", "hashOfConfig": "108"}, {"size": 1686, "mtime": 1746946499500, "results": "184", "hashOfConfig": "108"}, {"size": 5145, "mtime": 1746914029633, "results": "185", "hashOfConfig": "108"}, {"size": 10721, "mtime": 1748751269815, "results": "186", "hashOfConfig": "108"}, {"size": 22179, "mtime": 1747432554979, "results": "187", "hashOfConfig": "108"}, {"size": 2258, "mtime": 1746946368534, "results": "188", "hashOfConfig": "108"}, {"size": 4094, "mtime": 1748161663641, "results": "189", "hashOfConfig": "108"}, {"size": 5273, "mtime": 1747946737459, "results": "190", "hashOfConfig": "108"}, {"size": 4346, "mtime": 1747491472989, "results": "191", "hashOfConfig": "108"}, {"size": 15571, "mtime": 1747980774491, "results": "192", "hashOfConfig": "108"}, {"size": 6742, "mtime": 1748751174061, "results": "193", "hashOfConfig": "108"}, {"size": 6529, "mtime": 1748664406267, "results": "194", "hashOfConfig": "108"}, {"size": 15764, "mtime": 1748877145346, "results": "195", "hashOfConfig": "108"}, {"size": 6899, "mtime": 1748877131332, "results": "196", "hashOfConfig": "108"}, {"size": 5536, "mtime": 1748670096009, "results": "197", "hashOfConfig": "108"}, {"size": 5457, "mtime": 1748666884369, "results": "198", "hashOfConfig": "108"}, {"size": 5605, "mtime": 1748666925194, "results": "199", "hashOfConfig": "108"}, {"size": 77752, "mtime": 1748878387989, "results": "200", "hashOfConfig": "108"}, {"size": 2807, "mtime": 1748705699971, "results": "201", "hashOfConfig": "108"}, {"size": 23591, "mtime": 1748881382254, "results": "202", "hashOfConfig": "108"}, {"size": 3708, "mtime": 1748705727900, "results": "203", "hashOfConfig": "108"}, {"size": 10270, "mtime": 1748724524628, "results": "204", "hashOfConfig": "108"}, {"size": 8247, "mtime": 1748756088995, "results": "205", "hashOfConfig": "108"}, {"size": 11038, "mtime": 1748756003708, "results": "206", "hashOfConfig": "108"}, {"size": 15055, "mtime": 1748755908778, "results": "207", "hashOfConfig": "108"}, {"size": 16415, "mtime": 1748755956687, "results": "208", "hashOfConfig": "108"}, {"size": 3434, "mtime": 1748755857115, "results": "209", "hashOfConfig": "108"}, {"size": 3483, "mtime": 1748755829302, "results": "210", "hashOfConfig": "108"}, {"size": 3508, "mtime": 1748755842942, "results": "211", "hashOfConfig": "108"}, {"size": 956, "mtime": 1748878396989, "results": "212", "hashOfConfig": "108"}, {"size": 13327, "mtime": 1748881322351, "results": "213", "hashOfConfig": "108"}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["532"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", ["533", "534", "535", "536", "537"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["538", "539", "540", "541"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["542", "543", "544", "545", "546", "547", "548", "549", "550", "551"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["552"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["553", "554", "555", "556", "557", "558", "559", "560", "561", "562"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["578"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["579", "580", "581", "582", "583", "584", "585", "586"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["605"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["606"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["607", "608", "609"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["610", "611", "612", "613"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["614", "615"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["616", "617"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["618", "619", "620", "621"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["622", "623", "624", "625"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["626"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["627", "628", "629", "630", "631", "632", "633"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["634", "635", "636"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["637", "638", "639", "640", "641", "642", "643"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["644", "645"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["646", "647"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["648", "649"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["650", "651"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["652", "653", "654", "655", "656", "657", "658", "659"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["674", "675", "676", "677", "678", "679", "680"], ["681"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["705", "706"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["707", "708"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["709", "710"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["722"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["745"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["746"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["747"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["759"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["760", "761", "762", "763"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["804", "805"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["806"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["807", "808"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["829", "830", "831", "832", "833", "834", "835", "836"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["837"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["838", "839", "840"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["841", "842"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["843"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["844"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["845"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["846"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["847", "848", "849", "850", "851", "852"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["853"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js", ["854", "855", "856", "857", "858", "859", "860", "861"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js", ["862", "863", "864", "865", "866", "867", "868"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["869", "870", "871", "872", "873", "874", "875", "876", "877"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["878", "879", "880", "881", "882", "883", "884", "885", "886"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["887", "888", "889"], [], {"ruleId": "890", "severity": 1, "message": "891", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 14}, {"ruleId": "894", "severity": 1, "message": "895", "line": 97, "column": 71, "nodeType": "896", "messageId": "897", "endLine": 97, "endColumn": 100}, {"ruleId": "894", "severity": 1, "message": "895", "line": 98, "column": 70, "nodeType": "896", "messageId": "897", "endLine": 98, "endColumn": 99}, {"ruleId": "894", "severity": 1, "message": "895", "line": 99, "column": 67, "nodeType": "896", "messageId": "897", "endLine": 99, "endColumn": 96}, {"ruleId": "894", "severity": 1, "message": "895", "line": 100, "column": 76, "nodeType": "896", "messageId": "897", "endLine": 100, "endColumn": 105}, {"ruleId": "894", "severity": 1, "message": "895", "line": 101, "column": 71, "nodeType": "896", "messageId": "897", "endLine": 101, "endColumn": 100}, {"ruleId": "898", "severity": 1, "message": "899", "line": 78, "column": 11, "nodeType": "900", "messageId": "901", "endLine": 78, "endColumn": 115}, {"ruleId": "898", "severity": 1, "message": "899", "line": 80, "column": 11, "nodeType": "900", "messageId": "901", "endLine": 80, "endColumn": 107}, {"ruleId": "898", "severity": 1, "message": "899", "line": 86, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 86, "endColumn": 105}, {"ruleId": "898", "severity": 1, "message": "899", "line": 89, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 89, "endColumn": 41}, {"ruleId": "890", "severity": 1, "message": "902", "line": 13, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 13, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "903", "line": 20, "column": 25, "nodeType": "892", "messageId": "893", "endLine": 20, "endColumn": 34}, {"ruleId": "890", "severity": 1, "message": "904", "line": 21, "column": 19, "nodeType": "892", "messageId": "893", "endLine": 21, "endColumn": 35}, {"ruleId": "890", "severity": 1, "message": "905", "line": 22, "column": 12, "nodeType": "892", "messageId": "893", "endLine": 22, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "906", "line": 23, "column": 18, "nodeType": "892", "messageId": "893", "endLine": 23, "endColumn": 28}, {"ruleId": "890", "severity": 1, "message": "907", "line": 57, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 57, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "908", "line": 58, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 58, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "909", "line": 59, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 59, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "910", "line": 60, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 60, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "911", "line": 69, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 69, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "912", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "913", "line": 2, "column": 27, "nodeType": "892", "messageId": "893", "endLine": 2, "endColumn": 31}, {"ruleId": "890", "severity": 1, "message": "914", "line": 2, "column": 33, "nodeType": "892", "messageId": "893", "endLine": 2, "endColumn": 37}, {"ruleId": "890", "severity": 1, "message": "915", "line": 2, "column": 39, "nodeType": "892", "messageId": "893", "endLine": 2, "endColumn": 50}, {"ruleId": "890", "severity": 1, "message": "916", "line": 2, "column": 52, "nodeType": "892", "messageId": "893", "endLine": 2, "endColumn": 66}, {"ruleId": "890", "severity": 1, "message": "902", "line": 2, "column": 68, "nodeType": "892", "messageId": "893", "endLine": 2, "endColumn": 74}, {"ruleId": "890", "severity": 1, "message": "903", "line": 5, "column": 25, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 34}, {"ruleId": "890", "severity": 1, "message": "904", "line": 6, "column": 19, "nodeType": "892", "messageId": "893", "endLine": 6, "endColumn": 35}, {"ruleId": "890", "severity": 1, "message": "905", "line": 7, "column": 12, "nodeType": "892", "messageId": "893", "endLine": 7, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "906", "line": 8, "column": 18, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 28}, {"ruleId": "890", "severity": 1, "message": "917", "line": 43, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 43, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "914", "line": 8, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "891", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "913", "line": 11, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "918", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "919", "line": 15, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 15, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "920", "line": 16, "column": 15, "nodeType": "892", "messageId": "893", "endLine": 16, "endColumn": 27}, {"ruleId": "890", "severity": 1, "message": "921", "line": 17, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 17, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "922", "line": 18, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 18, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "923", "line": 19, "column": 13, "nodeType": "892", "messageId": "893", "endLine": 19, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "924", "line": 20, "column": 14, "nodeType": "892", "messageId": "893", "endLine": 20, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "925", "line": 25, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 25, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "926", "line": 28, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 28, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "927", "line": 48, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 48, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "928", "line": 53, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 53, "endColumn": 20}, {"ruleId": "890", "severity": 1, "message": "929", "line": 11, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "930", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "931", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "932", "line": 7, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 7, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "933", "line": 12, "column": 14, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "919", "line": 13, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 13, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "934", "line": 17, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 17, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "926", "line": 21, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 21, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "935", "line": 26, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 26, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "914", "line": 8, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "932", "line": 11, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "936", "line": 14, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 14, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "937", "line": 26, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 26, "endColumn": 16}, {"ruleId": "890", "severity": 1, "message": "938", "line": 30, "column": 15, "nodeType": "892", "messageId": "893", "endLine": 30, "endColumn": 27}, {"ruleId": "890", "severity": 1, "message": "939", "line": 32, "column": 14, "nodeType": "892", "messageId": "893", "endLine": 32, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "940", "line": 33, "column": 15, "nodeType": "892", "messageId": "893", "endLine": 33, "endColumn": 27}, {"ruleId": "890", "severity": 1, "message": "941", "line": 40, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 40, "endColumn": 16}, {"ruleId": "890", "severity": 1, "message": "926", "line": 46, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 46, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "942", "line": 48, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 48, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "935", "line": 50, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 50, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "943", "line": 205, "column": 19, "nodeType": "892", "messageId": "893", "endLine": 205, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "944", "line": 213, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 213, "endColumn": 28}, {"ruleId": "890", "severity": 1, "message": "945", "line": 214, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 214, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "946", "line": 214, "column": 25, "nodeType": "892", "messageId": "893", "endLine": 214, "endColumn": 41}, {"ruleId": "947", "severity": 1, "message": "948", "line": 574, "column": 6, "nodeType": "949", "endLine": 574, "endColumn": 15, "suggestions": "950"}, {"ruleId": "890", "severity": 1, "message": "951", "line": 579, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 579, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "952", "line": 1, "column": 27, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 36}, {"ruleId": "890", "severity": 1, "message": "953", "line": 49, "column": 19, "nodeType": "892", "messageId": "893", "endLine": 49, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "918", "line": 15, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 15, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "933", "line": 39, "column": 14, "nodeType": "892", "messageId": "893", "endLine": 39, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "954", "line": 43, "column": 16, "nodeType": "892", "messageId": "893", "endLine": 43, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "930", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "931", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "935", "line": 26, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 26, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "955", "line": 48, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 48, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "930", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "955", "line": 37, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 37, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "930", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "955", "line": 52, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 52, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "930", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "931", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "935", "line": 26, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 26, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "955", "line": 48, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 48, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "930", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "931", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "935", "line": 26, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 26, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "955", "line": 48, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 48, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "935", "line": 27, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 27, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "931", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "956", "line": 6, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 6, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "919", "line": 14, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 14, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "926", "line": 23, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 23, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "935", "line": 30, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 30, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "955", "line": 33, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 33, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "957", "line": 38, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 38, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "926", "line": 20, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 20, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "935", "line": 27, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 27, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "957", "line": 35, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 35, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "931", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "956", "line": 6, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 6, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "919", "line": 14, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 14, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "926", "line": 23, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 23, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "935", "line": 30, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 30, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "955", "line": 33, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 33, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "957", "line": 38, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 38, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "926", "line": 24, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 24, "endColumn": 26}, {"ruleId": "947", "severity": 1, "message": "958", "line": 53, "column": 6, "nodeType": "949", "endLine": 53, "endColumn": 18, "suggestions": "959"}, {"ruleId": "890", "severity": 1, "message": "960", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "961", "line": 5, "column": 7, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "918", "line": 14, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 14, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "962", "line": 28, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 28, "endColumn": 18}, {"ruleId": "890", "severity": 1, "message": "960", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "961", "line": 5, "column": 7, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "914", "line": 8, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "891", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "963", "line": 23, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 23, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "964", "line": 24, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 24, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "920", "line": 46, "column": 15, "nodeType": "892", "messageId": "893", "endLine": 46, "endColumn": 27}, {"ruleId": "890", "severity": 1, "message": "965", "line": 47, "column": 12, "nodeType": "892", "messageId": "893", "endLine": 47, "endColumn": 21}, {"ruleId": "947", "severity": 1, "message": "966", "line": 134, "column": 6, "nodeType": "949", "endLine": 134, "endColumn": 18, "suggestions": "967"}, {"ruleId": "890", "severity": 1, "message": "914", "line": 8, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "891", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "963", "line": 23, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 23, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "964", "line": 24, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 24, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "918", "line": 25, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 25, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "932", "line": 29, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 29, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "922", "line": 39, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 39, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "920", "line": 43, "column": 15, "nodeType": "892", "messageId": "893", "endLine": 43, "endColumn": 27}, {"ruleId": "890", "severity": 1, "message": "968", "line": 44, "column": 14, "nodeType": "892", "messageId": "893", "endLine": 44, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "969", "line": 50, "column": 69, "nodeType": "892", "messageId": "893", "endLine": 50, "endColumn": 76}, {"ruleId": "890", "severity": 1, "message": "970", "line": 79, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 79, "endColumn": 26}, {"ruleId": "947", "severity": 1, "message": "971", "line": 179, "column": 6, "nodeType": "949", "endLine": 179, "endColumn": 8, "suggestions": "972"}, {"ruleId": "890", "severity": 1, "message": "973", "line": 697, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 697, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "974", "line": 20, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 20, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "975", "line": 21, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 21, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "976", "line": 22, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 22, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "913", "line": 23, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 23, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "977", "line": 26, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 26, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "978", "line": 69, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 69, "endColumn": 22}, {"ruleId": "979", "severity": 1, "message": "980", "line": 466, "column": 9, "nodeType": "981", "messageId": "982", "endLine": 469, "endColumn": 10}, {"ruleId": "947", "severity": 1, "message": "983", "line": 95, "column": 6, "nodeType": "949", "endLine": 95, "endColumn": 21, "suggestions": "984", "suppressions": "985"}, {"ruleId": "898", "severity": 1, "message": "899", "line": 260, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 264, "endColumn": 11}, {"ruleId": "898", "severity": 1, "message": "899", "line": 274, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 274, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 278, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 278, "endColumn": 54}, {"ruleId": "898", "severity": 1, "message": "899", "line": 333, "column": 11, "nodeType": "900", "messageId": "901", "endLine": 338, "endColumn": 13}, {"ruleId": "898", "severity": 1, "message": "899", "line": 435, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 439, "endColumn": 11}, {"ruleId": "898", "severity": 1, "message": "899", "line": 451, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 451, "endColumn": 54}, {"ruleId": "898", "severity": 1, "message": "899", "line": 668, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 668, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 677, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 677, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 681, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 681, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "986", "line": 755, "column": 17, "nodeType": "892", "messageId": "893", "endLine": 755, "endColumn": 22}, {"ruleId": "898", "severity": 1, "message": "899", "line": 775, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 779, "endColumn": 11}, {"ruleId": "898", "severity": 1, "message": "899", "line": 794, "column": 11, "nodeType": "900", "messageId": "901", "endLine": 798, "endColumn": 13}, {"ruleId": "898", "severity": 1, "message": "899", "line": 801, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 804, "endColumn": 11}, {"ruleId": "898", "severity": 1, "message": "899", "line": 810, "column": 11, "nodeType": "900", "messageId": "901", "endLine": 814, "endColumn": 13}, {"ruleId": "898", "severity": 1, "message": "899", "line": 817, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 820, "endColumn": 11}, {"ruleId": "898", "severity": 1, "message": "899", "line": 885, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 889, "endColumn": 11}, {"ruleId": "987", "severity": 1, "message": "988", "line": 955, "column": 3, "nodeType": "989", "messageId": "990", "endLine": 955, "endColumn": 29}, {"ruleId": "987", "severity": 1, "message": "991", "line": 1143, "column": 3, "nodeType": "989", "messageId": "990", "endLine": 1143, "endColumn": 23}, {"ruleId": "987", "severity": 1, "message": "992", "line": 1238, "column": 3, "nodeType": "989", "messageId": "990", "endLine": 1238, "endColumn": 20}, {"ruleId": "898", "severity": 1, "message": "899", "line": 1287, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 1287, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 1317, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 1317, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 1370, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 1370, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 1412, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 1412, "endColumn": 163}, {"ruleId": "890", "severity": 1, "message": "993", "line": 6, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 6, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "918", "line": 11, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "930", "line": 2, "column": 15, "nodeType": "892", "messageId": "893", "endLine": 2, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "994", "line": 16, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 16, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "960", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "961", "line": 5, "column": 7, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "995", "line": 3, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 3, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "996", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 6}, {"ruleId": "890", "severity": 1, "message": "997", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "998", "line": 6, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 6, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "999", "line": 7, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 7, "endColumn": 6}, {"ruleId": "890", "severity": 1, "message": "1000", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "1001", "line": 36, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 36, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "1002", "line": 50, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 50, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1003", "line": 64, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 64, "endColumn": 20}, {"ruleId": "890", "severity": 1, "message": "1004", "line": 88, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 88, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "1005", "line": 104, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 104, "endColumn": 30}, {"ruleId": "890", "severity": 1, "message": "1006", "line": 3, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 3, "endColumn": 12}, {"ruleId": "890", "severity": 1, "message": "998", "line": 3, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 3, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "999", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 6}, {"ruleId": "890", "severity": 1, "message": "1007", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "1008", "line": 6, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 6, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "1009", "line": 7, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 7, "endColumn": 16}, {"ruleId": "890", "severity": 1, "message": "1010", "line": 8, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "1000", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "1011", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "995", "line": 11, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "996", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 6}, {"ruleId": "890", "severity": 1, "message": "997", "line": 13, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 13, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "1012", "line": 14, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 14, "endColumn": 16}, {"ruleId": "890", "severity": 1, "message": "1013", "line": 15, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 15, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "1006", "line": 16, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 16, "endColumn": 12}, {"ruleId": "890", "severity": 1, "message": "1014", "line": 18, "column": 40, "nodeType": "892", "messageId": "893", "endLine": 18, "endColumn": 44}, {"ruleId": "890", "severity": 1, "message": "1015", "line": 47, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 47, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1016", "line": 64, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 64, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1017", "line": 71, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 71, "endColumn": 20}, {"ruleId": "890", "severity": 1, "message": "1004", "line": 79, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 79, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "1005", "line": 95, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 95, "endColumn": 30}, {"ruleId": "890", "severity": 1, "message": "1018", "line": 299, "column": 27, "nodeType": "892", "messageId": "893", "endLine": 299, "endColumn": 37}, {"ruleId": "890", "severity": 1, "message": "1019", "line": 300, "column": 27, "nodeType": "892", "messageId": "893", "endLine": 300, "endColumn": 36}, {"ruleId": "890", "severity": 1, "message": "931", "line": 3, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 3, "endColumn": 8}, {"ruleId": "947", "severity": 1, "message": "1020", "line": 54, "column": 6, "nodeType": "949", "endLine": 54, "endColumn": 34, "suggestions": "1021"}, {"ruleId": "890", "severity": 1, "message": "1022", "line": 25, "column": 13, "nodeType": "892", "messageId": "893", "endLine": 25, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "1023", "line": 33, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 33, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "1024", "line": 34, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 34, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "1025", "line": 35, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 35, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "1026", "line": 36, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 36, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "1027", "line": 37, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 37, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1028", "line": 41, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 41, "endColumn": 20}, {"ruleId": "890", "severity": 1, "message": "1029", "line": 43, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 43, "endColumn": 34}, {"ruleId": "890", "severity": 1, "message": "1030", "line": 69, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 69, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1031", "line": 69, "column": 19, "nodeType": "892", "messageId": "893", "endLine": 69, "endColumn": 29}, {"ruleId": "947", "severity": 1, "message": "1032", "line": 88, "column": 6, "nodeType": "949", "endLine": 88, "endColumn": 18, "suggestions": "1033"}, {"ruleId": "947", "severity": 1, "message": "1034", "line": 448, "column": 6, "nodeType": "949", "endLine": 448, "endColumn": 28, "suggestions": "1035"}, {"ruleId": "890", "severity": 1, "message": "1036", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 12}, {"ruleId": "890", "severity": 1, "message": "1037", "line": 8, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "1038", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "1039", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 15}, {"ruleId": "947", "severity": 1, "message": "1020", "line": 46, "column": 6, "nodeType": "949", "endLine": 46, "endColumn": 18, "suggestions": "1040"}, {"ruleId": "890", "severity": 1, "message": "1041", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "974", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "975", "line": 11, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "976", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "1039", "line": 33, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 33, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "1042", "line": 35, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 35, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "968", "line": 42, "column": 14, "nodeType": "892", "messageId": "893", "endLine": 42, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "1023", "line": 52, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 52, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "1024", "line": 53, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 53, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "1025", "line": 54, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 54, "endColumn": 22}, {"ruleId": "890", "severity": 1, "message": "1026", "line": 55, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 55, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "1027", "line": 56, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 56, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1043", "line": 57, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 57, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "1044", "line": 58, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 58, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1045", "line": 59, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 59, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "942", "line": 72, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 72, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1046", "line": 79, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 79, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "1047", "line": 79, "column": 25, "nodeType": "892", "messageId": "893", "endLine": 79, "endColumn": 41}, {"ruleId": "890", "severity": 1, "message": "1048", "line": 80, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 80, "endColumn": 27}, {"ruleId": "890", "severity": 1, "message": "1049", "line": 85, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 85, "endColumn": 26}, {"ruleId": "947", "severity": 1, "message": "1020", "line": 105, "column": 6, "nodeType": "949", "endLine": 105, "endColumn": 18, "suggestions": "1050"}, {"ruleId": "947", "severity": 1, "message": "1051", "line": 112, "column": 6, "nodeType": "949", "endLine": 112, "endColumn": 20, "suggestions": "1052"}, {"ruleId": "947", "severity": 1, "message": "1053", "line": 127, "column": 6, "nodeType": "949", "endLine": 127, "endColumn": 34, "suggestions": "1054"}, {"ruleId": "890", "severity": 1, "message": "1055", "line": 283, "column": 13, "nodeType": "892", "messageId": "893", "endLine": 283, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "977", "line": 17, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 17, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1039", "line": 34, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 34, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "1042", "line": 35, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 35, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "1056", "line": 39, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 39, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1023", "line": 51, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 51, "endColumn": 15}, {"ruleId": "890", "severity": 1, "message": "1024", "line": 52, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 52, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "1026", "line": 54, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 54, "endColumn": 21}, {"ruleId": "890", "severity": 1, "message": "1027", "line": 55, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 55, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1029", "line": 62, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 62, "endColumn": 34}, {"ruleId": "890", "severity": 1, "message": "1057", "line": 105, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 105, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "1058", "line": 105, "column": 28, "nodeType": "892", "messageId": "893", "endLine": 105, "endColumn": 47}, {"ruleId": "947", "severity": 1, "message": "1051", "line": 145, "column": 6, "nodeType": "949", "endLine": 145, "endColumn": 18, "suggestions": "1059"}, {"ruleId": "890", "severity": 1, "message": "1060", "line": 701, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 701, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1061", "line": 1311, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 1311, "endColumn": 28}, {"ruleId": "890", "severity": 1, "message": "1062", "line": 1316, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 1316, "endColumn": 30}, {"ruleId": "890", "severity": 1, "message": "1063", "line": 1883, "column": 9, "nodeType": "892", "messageId": "893", "endLine": 1883, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "960", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "961", "line": 5, "column": 7, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "1064", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "960", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "961", "line": 5, "column": 7, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "960", "line": 1, "column": 8, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "961", "line": 5, "column": 7, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "1065", "line": 83, "column": 13, "nodeType": "892", "messageId": "893", "endLine": 83, "endColumn": 21}, {"ruleId": "898", "severity": 1, "message": "899", "line": 109, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 109, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 123, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 123, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 127, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 127, "endColumn": 54}, {"ruleId": "898", "severity": 1, "message": "899", "line": 212, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 212, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 226, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 226, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 230, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 230, "endColumn": 54}, {"ruleId": "898", "severity": 1, "message": "899", "line": 271, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 271, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 280, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 280, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 284, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 284, "endColumn": 54}, {"ruleId": "898", "severity": 1, "message": "899", "line": 320, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 320, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 324, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 324, "endColumn": 54}, {"ruleId": "898", "severity": 1, "message": "899", "line": 360, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 360, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 369, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 369, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 373, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 373, "endColumn": 54}, {"ruleId": "898", "severity": 1, "message": "899", "line": 450, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 450, "endColumn": 163}, {"ruleId": "898", "severity": 1, "message": "899", "line": 459, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 459, "endColumn": 70}, {"ruleId": "898", "severity": 1, "message": "899", "line": 463, "column": 9, "nodeType": "900", "messageId": "901", "endLine": 463, "endColumn": 54}, {"ruleId": "890", "severity": 1, "message": "1066", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "921", "line": 27, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 27, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "937", "line": 30, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 30, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1025", "line": 34, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 34, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "1030", "line": 49, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 49, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1031", "line": 49, "column": 19, "nodeType": "892", "messageId": "893", "endLine": 49, "endColumn": 29}, {"ruleId": "947", "severity": 1, "message": "1020", "line": 64, "column": 6, "nodeType": "949", "endLine": 64, "endColumn": 32, "suggestions": "1067"}, {"ruleId": "890", "severity": 1, "message": "1068", "line": 270, "column": 17, "nodeType": "892", "messageId": "893", "endLine": 270, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "1069", "line": 17, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 17, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "976", "line": 16, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 16, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "975", "line": 17, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 17, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "974", "line": 19, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 19, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "937", "line": 14, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 14, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1070", "line": 43, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 43, "endColumn": 26}, {"ruleId": "890", "severity": 1, "message": "932", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "1071", "line": 33, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 33, "endColumn": 29}, {"ruleId": "890", "severity": 1, "message": "1072", "line": 3, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 3, "endColumn": 6}, {"ruleId": "890", "severity": 1, "message": "918", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "1073", "line": 20, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 20, "endColumn": 19}, {"ruleId": "947", "severity": 1, "message": "1074", "line": 136, "column": 6, "nodeType": "949", "endLine": 136, "endColumn": 18, "suggestions": "1075"}, {"ruleId": "947", "severity": 1, "message": "1076", "line": 141, "column": 6, "nodeType": "949", "endLine": 141, "endColumn": 52, "suggestions": "1077"}, {"ruleId": "947", "severity": 1, "message": "1078", "line": 146, "column": 6, "nodeType": "949", "endLine": 146, "endColumn": 62, "suggestions": "1079"}, {"ruleId": "947", "severity": 1, "message": "1080", "line": 151, "column": 6, "nodeType": "949", "endLine": 151, "endColumn": 28, "suggestions": "1081"}, {"ruleId": "947", "severity": 1, "message": "1082", "line": 160, "column": 6, "nodeType": "949", "endLine": 160, "endColumn": 39, "suggestions": "1083"}, {"ruleId": "947", "severity": 1, "message": "1084", "line": 68, "column": 6, "nodeType": "949", "endLine": 68, "endColumn": 18, "suggestions": "1085"}, {"ruleId": "890", "severity": 1, "message": "914", "line": 8, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 8, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 9, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 9, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "956", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 9}, {"ruleId": "890", "severity": 1, "message": "1038", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 11}, {"ruleId": "890", "severity": 1, "message": "1086", "line": 21, "column": 17, "nodeType": "892", "messageId": "893", "endLine": 21, "endColumn": 31}, {"ruleId": "890", "severity": 1, "message": "906", "line": 24, "column": 17, "nodeType": "892", "messageId": "893", "endLine": 24, "endColumn": 27}, {"ruleId": "947", "severity": 1, "message": "1087", "line": 180, "column": 6, "nodeType": "949", "endLine": 180, "endColumn": 25, "suggestions": "1088"}, {"ruleId": "894", "severity": 1, "message": "1089", "line": 243, "column": 15, "nodeType": "896", "messageId": "897", "endLine": 248, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "931", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "964", "line": 15, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 15, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "918", "line": 16, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 16, "endColumn": 10}, {"ruleId": "890", "severity": 1, "message": "922", "line": 28, "column": 11, "nodeType": "892", "messageId": "893", "endLine": 28, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "923", "line": 29, "column": 13, "nodeType": "892", "messageId": "893", "endLine": 29, "endColumn": 23}, {"ruleId": "890", "severity": 1, "message": "1090", "line": 39, "column": 34, "nodeType": "892", "messageId": "893", "endLine": 39, "endColumn": 59}, {"ruleId": "890", "severity": 1, "message": "1030", "line": 41, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 41, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "952", "line": 1, "column": 27, "nodeType": "892", "messageId": "893", "endLine": 1, "endColumn": 36}, {"ruleId": "890", "severity": 1, "message": "914", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 11, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "930", "line": 12, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 12, "endColumn": 13}, {"ruleId": "890", "severity": 1, "message": "1069", "line": 27, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 27, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "921", "line": 30, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 30, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1091", "line": 33, "column": 17, "nodeType": "892", "messageId": "893", "endLine": 33, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "906", "line": 34, "column": 17, "nodeType": "892", "messageId": "893", "endLine": 34, "endColumn": 27}, {"ruleId": "890", "severity": 1, "message": "933", "line": 35, "column": 14, "nodeType": "892", "messageId": "893", "endLine": 35, "endColumn": 25}, {"ruleId": "890", "severity": 1, "message": "914", "line": 10, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 10, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 11, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 11, "endColumn": 14}, {"ruleId": "890", "severity": 1, "message": "1069", "line": 27, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 27, "endColumn": 8}, {"ruleId": "890", "severity": 1, "message": "1092", "line": 28, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 28, "endColumn": 12}, {"ruleId": "890", "severity": 1, "message": "1093", "line": 29, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 29, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "1094", "line": 30, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 30, "endColumn": 19}, {"ruleId": "890", "severity": 1, "message": "921", "line": 34, "column": 10, "nodeType": "892", "messageId": "893", "endLine": 34, "endColumn": 17}, {"ruleId": "890", "severity": 1, "message": "1095", "line": 37, "column": 17, "nodeType": "892", "messageId": "893", "endLine": 37, "endColumn": 31}, {"ruleId": "947", "severity": 1, "message": "1096", "line": 98, "column": 6, "nodeType": "949", "endLine": 98, "endColumn": 24, "suggestions": "1097"}, {"ruleId": "890", "severity": 1, "message": "914", "line": 4, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 4, "endColumn": 7}, {"ruleId": "890", "severity": 1, "message": "915", "line": 5, "column": 3, "nodeType": "892", "messageId": "893", "endLine": 5, "endColumn": 14}, {"ruleId": "947", "severity": 1, "message": "1098", "line": 66, "column": 6, "nodeType": "949", "endLine": 66, "endColumn": 25, "suggestions": "1099"}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "react/jsx-pascal-case", "Imported JSX component CertificazioneCEI64_8Page must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1100"], "'handleOpenDetails' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'InventoryIcon' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1101"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["1102"], "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1103"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1104"], ["1105"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'filteredCavi' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1106"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1107"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1108"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["1109"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["1110"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1111"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1112"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["1113"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["1114"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1115"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1116"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1117"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1118"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1119"], "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1120"], "'AssignmentIcon' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'getInitialAction'. Either include it or remove the dependency array.", ["1121"], "Imported JSX component CertificazioneCEI64_8 must be in PascalCase or SCREAMING_SNAKE_CASE", "'setSelectedCertificazione' is assigned a value but never used.", "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1122"], "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1123"], {"desc": "1124", "fix": "1125"}, {"desc": "1126", "fix": "1127"}, {"desc": "1128", "fix": "1129"}, {"desc": "1130", "fix": "1131"}, {"desc": "1132", "fix": "1133"}, {"kind": "1134", "justification": "1135"}, {"desc": "1136", "fix": "1137"}, {"desc": "1138", "fix": "1139"}, {"desc": "1140", "fix": "1141"}, {"desc": "1142", "fix": "1143"}, {"desc": "1142", "fix": "1144"}, {"desc": "1145", "fix": "1146"}, {"desc": "1147", "fix": "1148"}, {"desc": "1149", "fix": "1150"}, {"desc": "1151", "fix": "1152"}, {"desc": "1153", "fix": "1154"}, {"desc": "1155", "fix": "1156"}, {"desc": "1157", "fix": "1158"}, {"desc": "1159", "fix": "1160"}, {"desc": "1161", "fix": "1162"}, {"desc": "1163", "fix": "1164"}, {"desc": "1165", "fix": "1166"}, {"desc": "1167", "fix": "1168"}, {"desc": "1169", "fix": "1170"}, "Update the dependencies array to be: [caviAttivi, caviSpare, error, filters, user]", {"range": "1171", "text": "1172"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1173", "text": "1174"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1175", "text": "1176"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1177", "text": "1178"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "1179", "text": "1180"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1181", "text": "1182"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1183", "text": "1184"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1185", "text": "1186"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1187", "text": "1188"}, {"range": "1189", "text": "1188"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1190", "text": "1191"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1192", "text": "1193"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1194", "text": "1195"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1196", "text": "1197"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1200", "text": "1201"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1202", "text": "1203"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1204", "text": "1205"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1206", "text": "1207"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1208", "text": "1209"}, "Update the dependencies array to be: [getInitialAction, location.pathname]", {"range": "1210", "text": "1211"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1212", "text": "1213"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1214", "text": "1215"}, [23147, 23156], "[caviAttivi, caviSpare, error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [6583, 6585], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]", [3608, 3620], "[cantiereId, loadInitialData]", [3705, 3751], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [3835, 3891], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [3997, 4019], "[calculateStatistics, cavi, certificazioni]", [4241, 4274], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1648, 1660], "[cantiereId, loadComande, loadStatistiche]", [4982, 5001], "[getInitialAction, location.pathname]", [2516, 2534], "[certificazioneId, loadProve]", [1523, 1542], "[loadCaviDisponibili, open, tipoComanda]"]