import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const comandeService = {
  // Ottiene la lista delle comande di un cantiere
  getComande: async (cantiereId, params = {}) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, { params });
      return response.data;
    } catch (error) {
      console.error('Get comande error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una nuova comanda
  createComanda: async (comandaData) => {
    try {
      const response = await axiosInstance.post('/comande/', comandaData);
      return response.data;
    } catch (error) {
      console.error('Create comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i dettagli di una comanda
  getDettagliComanda: async (codiceComanda) => {
    try {
      const response = await axiosInstance.get(`/comande/${codiceComanda}`);
      return response.data;
    } catch (error) {
      console.error('Get dettagli comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Assegna cavi a una comanda
  assegnaCavi: async (codiceComanda, listaIdCavi) => {
    try {
      const response = await axiosInstance.post(`/comande/${codiceComanda}/assegna-cavi`, {
        lista_id_cavi: listaIdCavi
      });
      return response.data;
    } catch (error) {
      console.error('Assegna cavi error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di posa
  aggiornaDatiPosa: async (codiceComanda, datiPosa) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-posa`, {
        dati_posa: datiPosa
      });
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati posa error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di collegamento
  aggiornaDatiCollegamento: async (codiceComanda, datiCollegamento) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/dati-collegamento`, {
        dati_collegamento: datiCollegamento
      });
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati collegamento error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Cambia lo stato di una comanda
  cambiaStato: async (codiceComanda, nuovoStato) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/stato`, {
        nuovo_stato: nuovoStato
      });
      return response.data;
    } catch (error) {
      console.error('Cambia stato error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene le statistiche delle comande per un cantiere
  getStatisticheComande: async (cantiereId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/statistiche`);
      return response.data;
    } catch (error) {
      console.error('Get statistiche comande error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna una comanda esistente
  updateComanda: async (cantiereId, idComanda, comandaData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`, comandaData);
      return response.data;
    } catch (error) {
      console.error('Update comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina una comanda
  deleteComanda: async (codiceComanda) => {
    try {
      const response = await axiosInstance.delete(`/comande/${codiceComanda}`);
      return response.data;
    } catch (error) {
      console.error('Delete comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Assegna una comanda a un cavo
  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {
        id_cavo: idCavo
      });
      return response.data;
    } catch (error) {
      console.error('Assign comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Genera PDF di una comanda
  printComanda: async (cantiereId, idComanda) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);
      return response.data;
    } catch (error) {
      console.error('Print comanda error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i cavi disponibili per un tipo di comanda
  getCaviDisponibili: async (cantiereId, tipoComanda) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}/cavi-disponibili`, {
        params: { tipo_comanda: tipoComanda }
      });
      return response.data;
    } catch (error) {
      console.error('Get cavi disponibili error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una comanda con cavi pre-selezionati
  createComandaConCavi: async (cantiereId, comandaData, listaIdCavi) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/comande/cantiere/${cantiereIdNum}/crea-con-cavi`, comandaData, {
        params: { lista_id_cavi: listaIdCavi }
      });
      return response.data;
    } catch (error) {
      console.error('Create comanda con cavi error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna i dati di certificazione
  aggiornaDatiCertificazione: async (codiceComanda, datiCertificazione) => {
    try {
      const response = await axiosInstance.put(`/comande/${codiceComanda}/certificazione`, datiCertificazione);
      return response.data;
    } catch (error) {
      console.error('Aggiorna dati certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default comandeService;
