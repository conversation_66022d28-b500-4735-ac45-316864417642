/* CABLYS Module Pages Styles */

/* Module Hero Section */
.module-hero {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  color: white;
  padding: 80px 0;
  text-align: center;
}

.module-hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.module-hero-content .module-icon {
  margin-bottom: 20px;
}

.module-hero-content .module-icon img {
  width: 80px;
  height: 80px;
  background-color: white;
  padding: 10px;
  border-radius: 50%;
}

.module-hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.module-hero-content p {
  font-size: 1.2rem;
  margin-bottom: 0;
  opacity: 0.9;
}

/* Module Overview Section */
.module-overview {
  padding: 60px 0;
}

.module-section {
  margin-bottom: 60px;
}

.module-section h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--primary-color);
  position: relative;
  padding-bottom: 10px;
}

.module-section h2::after {
  content: '';
  position: absolute;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  bottom: 0;
  left: 0;
}

.module-section > p {
  margin-bottom: 30px;
  max-width: 900px;
  line-height: 1.7;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.feature-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  padding: 25px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 600;
}

/* Functionality Sections */
.functionality {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 50px;
  align-items: center;
}

.functionality.reverse {
  direction: rtl;
}

.functionality.reverse .functionality-content,
.functionality.reverse .functionality-image {
  direction: ltr;
}

.functionality-content h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 600;
}

.functionality-content p {
  margin-bottom: 20px;
  line-height: 1.7;
}

.functionality-content ul {
  padding-left: 20px;
}

.functionality-content ul li {
  margin-bottom: 10px;
  position: relative;
}

.functionality-image img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Specs Grid */
.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.spec-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  padding: 25px;
}

.spec-card h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 600;
}

/* Case Studies */
.case-studies {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

.case-study {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.case-study h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 600;
}

.case-study p {
  line-height: 1.7;
}

/* CTA Section */
.module-cta {
  background-color: var(--primary-color);
  color: white;
  padding: 40px;
  border-radius: 8px;
  text-align: center;
  margin-top: 60px;
}

.module-cta h2 {
  margin-bottom: 15px;
  font-weight: 600;
}

.module-cta p {
  margin-bottom: 25px;
  opacity: 0.9;
}

.module-cta .btn-primary {
  background-color: white;
  color: var(--primary-color);
  border: none;
}

.module-cta .btn-primary:hover {
  background-color: var(--background-color);
  transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .functionality {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .functionality.reverse {
    direction: ltr;
  }
  
  .functionality-image {
    order: -1;
  }
  
  .functionality.reverse .functionality-image {
    order: -1;
  }
  
  .module-hero {
    padding: 60px 0;
  }
  
  .module-hero-content h1 {
    font-size: 2rem;
  }
}

/* Logo container link styles */
.logo-container a {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-container a h1 {
  color: var(--primary-color);
}