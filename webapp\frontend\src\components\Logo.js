import React from 'react';

const Logo = ({ width = 40, height = 40, color = '#1976d2' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Cable coil shape */}
      <circle cx="50" cy="50" r="45" stroke={color} strokeWidth="6" fill="none" />
      <path
        d="M50 5 L50 30 M50 70 L50 95"
        stroke={color}
        strokeWidth="6"
        strokeLinecap="round"
      />
      <path
        d="M5 50 L30 50 M70 50 L95 50"
        stroke={color}
        strokeWidth="6"
        strokeLinecap="round"
      />
      
      {/* Lightning bolt in the center */}
      <path
        d="M55 30 L40 50 L55 50 L45 70"
        stroke={color}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </svg>
  );
};

export default Logo;