#!/usr/bin/env python3
"""
Test script per verificare la normalizzazione dei dati delle bobine
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'webapp'))

from webapp.backend.utils.cable_normalizer import CableNormalizer

def test_normalizer():
    """Test della normalizzazione con i dati dell'esempio"""

    normalizer = CableNormalizer()

    # Test data simili a quelli inseriti dall'utente
    test_data = {
        'utility': 'POWER',
        'tipologia': 'LIYCI',  # Errore di battitura per LIYCY
        'sezione': '3X2.5MM2+2,5Yg',  # Errore di battitura per YG
        'n_conduttori': '3',
        'ubicazione_bobina': 'TBD',
        'fornitore': 'TBD',
        'n_DDT': 'TBD'
    }

    print("=== TEST NORMALIZZAZIONE BOBINA ===")
    print(f"Dati originali: {test_data}")
    print()

    # Applica la normalizzazione
    normalized_data = normalizer.normalize_all_cable_fields(test_data)

    print("=== RISULTATI NORMALIZZAZIONE ===")
    for field, original_value in test_data.items():
        normalized_value = normalized_data.get(field, original_value)
        if original_value != normalized_value:
            print(f"✅ {field}: '{original_value}' → '{normalized_value}'")
        else:
            print(f"⚪ {field}: '{original_value}' (non modificato)")

    print()
    print("=== TEST SPECIFICI ===")

    # Test specifico per tipologia
    print("1. Test tipologia LIYCI:")
    tipologia_result = normalizer.normalize_tipologia('LIYCI')
    print(f"   'LIYCI' → '{tipologia_result}'")

    # Test correzione automatica
    print("2. Test correzione automatica tipologia:")
    auto_correct_result = normalizer.auto_correct_value('LIYCI', 'tipologia')
    print(f"   'LIYCI' → '{auto_correct_result}'")

    # Test sezione
    print("3. Test sezione:")
    sezione_result = normalizer.normalize_section('3X2.5MM2+2,5Yg')
    print(f"   '3X2.5MM2+2,5Yg' → '{sezione_result}'")

    # Test similarità
    print("4. Test similarità LIYCI vs LIYCY:")
    similarity = normalizer.calculate_similarity('LIYCI', 'LIYCY')
    print(f"   Similarità: {similarity:.3f}")

    # Test best match
    print("5. Test best match:")
    best_match = normalizer.find_best_match('LIYCI', normalizer.known_tipologie, 0.6)
    print(f"   Best match per 'LIYCI': '{best_match}'")

def test_update_scenario():
    """Test specifico per lo scenario di aggiornamento bobina"""

    normalizer = CableNormalizer()

    print("\n=== TEST SCENARIO AGGIORNAMENTO BOBINA ===")

    # Simula i dati che arrivano dal frontend durante l'aggiornamento
    update_data = {
        'tipologia': 'LIYCy',  # Errore di battitura (y minuscola)
        'sezione': '3X2,5MM2+2,5Yg'  # Errori: virgola invece di punto, Yg invece di YG
    }

    print(f"Dati di aggiornamento originali: {update_data}")

    # Applica la normalizzazione come nel backend
    normalized_data = normalizer.normalize_all_cable_fields(update_data)

    print(f"Dati normalizzati: {normalized_data}")

    # Simula il processo di aggiornamento del backend
    for field in ['utility', 'tipologia', 'n_conduttori', 'sezione', 'ubicazione_bobina', 'fornitore', 'n_DDT']:
        if field in update_data and field in normalized_data:
            original = update_data[field]
            normalized = normalized_data[field]
            update_data[field] = normalized
            if original != normalized:
                print(f"✅ Campo {field} normalizzato: '{original}' → '{normalized}'")
            else:
                print(f"⚪ Campo {field} non modificato: '{original}'")

    print(f"Dati finali per l'aggiornamento: {update_data}")

    return update_data

if __name__ == "__main__":
    test_normalizer()
    test_update_scenario()
