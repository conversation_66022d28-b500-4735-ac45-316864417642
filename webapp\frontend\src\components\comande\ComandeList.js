import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Grid,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Assignment as AssignIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import CreaComandaConCavi from './CreaComandaConCavi';

const ComandeList = ({ cantiereId, cantiereName }) => {
  const [comande, setComande] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedComanda, setSelectedComanda] = useState(null);
  const [dialogMode, setDialogMode] = useState('create'); // 'create', 'edit', 'view'
  const [formData, setFormData] = useState({
    tipo_comanda: 'POSA',
    descrizione: '',
    responsabile: '',
    data_scadenza: '',
    priorita: 'NORMALE',
    note_capo_cantiere: ''
  });
  const [statistiche, setStatistiche] = useState(null);
  const [caviAssegnazione, setCaviAssegnazione] = useState('');
  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);

  // Carica le comande al mount del componente
  useEffect(() => {
    if (cantiereId) {
      loadComande();
      loadStatistiche();
    }
  }, [cantiereId]);

  const loadComande = async () => {
    try {
      setLoading(true);
      const response = await comandeService.getComande(cantiereId);
      setComande(response.comande || []);
      setError(null);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
      setError('Errore nel caricamento delle comande');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistiche = async () => {
    try {
      const stats = await comandeService.getStatisticheComande(cantiereId);
      setStatistiche(stats);
    } catch (err) {
      console.error('Errore nel caricamento delle statistiche:', err);
    }
  };

  const handleOpenDialog = (mode, comanda = null) => {
    setDialogMode(mode);
    setSelectedComanda(comanda);
    
    if (mode === 'create') {
      setFormData({
        tipo_comanda: 'POSA',
        descrizione: '',
        responsabile: '',
        data_scadenza: '',
        priorita: 'NORMALE',
        note_capo_cantiere: ''
      });
    } else if (mode === 'edit' && comanda) {
      setFormData({
        tipo_comanda: comanda.tipo_comanda,
        descrizione: comanda.descrizione || '',
        responsabile: comanda.responsabile || '',
        data_scadenza: comanda.data_scadenza || '',
        priorita: comanda.priorita || 'NORMALE',
        note_capo_cantiere: comanda.note_capo_cantiere || ''
      });
    }
    
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedComanda(null);
    setCaviAssegnazione('');
    setFormData({
      tipo_comanda: 'POSA',
      descrizione: '',
      responsabile: '',
      data_scadenza: '',
      priorita: 'NORMALE',
      note_capo_cantiere: ''
    });
  };

  const handleSubmit = async () => {
    try {
      if (dialogMode === 'create') {
        // Validazione responsabile obbligatorio
        if (!formData.responsabile.trim()) {
          setError('Il responsabile è obbligatorio');
          return;
        }

        await comandeService.createComanda({
          ...formData,
          id_cantiere: parseInt(cantiereId)
        });
      } else if (dialogMode === 'edit') {
        // Implementare update quando sarà disponibile l'endpoint
        console.log('Update non ancora implementato');
      } else if (dialogMode === 'assign') {
        // Assegnazione cavi
        if (!caviAssegnazione.trim()) {
          setError('Inserisci almeno un ID cavo');
          return;
        }

        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);
        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);
      }

      handleCloseDialog();
      loadComande();
      loadStatistiche();
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError('Errore nel salvataggio della comanda');
    }
  };

  const handleDelete = async (codiceComanda) => {
    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {
      try {
        await comandeService.deleteComanda(codiceComanda);
        loadComande();
        loadStatistiche();
      } catch (err) {
        console.error('Errore nell\'eliminazione:', err);
        setError('Errore nell\'eliminazione della comanda');
      }
    }
  };

  const getStatoColor = (stato) => {
    switch (stato) {
      case 'CREATA': return 'default';
      case 'ASSEGNATA': return 'primary';
      case 'IN_CORSO': return 'warning';
      case 'COMPLETATA': return 'success';
      case 'ANNULLATA': return 'error';
      default: return 'default';
    }
  };

  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Posa';
      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';
      case 'CERTIFICAZIONE': return 'Certificazione';
      case 'TESTING': return 'Testing/Certificazione';
      default: return tipo;
    }
  };

  const getPrioritaColor = (priorita) => {
    switch (priorita) {
      case 'BASSA': return 'default';
      case 'NORMALE': return 'primary';
      case 'ALTA': return 'warning';
      case 'URGENTE': return 'error';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header con statistiche */}
      <Box mb={3}>
        
        {statistiche && (
          <Grid container spacing={2} mb={2}>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Totale
                  </Typography>
                  <Typography variant="h5">
                    {statistiche.totale_comande}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Create
                  </Typography>
                  <Typography variant="h5">
                    {statistiche.comande_create}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Assegnate
                  </Typography>
                  <Typography variant="h5">
                    {statistiche.comande_assegnate}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    In Corso
                  </Typography>
                  <Typography variant="h5">
                    {statistiche.comande_in_corso}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Completate
                  </Typography>
                  <Typography variant="h5">
                    {statistiche.comande_completate}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    % Completamento
                  </Typography>
                  <Typography variant="h5">
                    {statistiche.percentuale_completamento_medio.toFixed(1)}%
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}
      </Box>

      {/* Toolbar */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2} flexWrap="wrap" gap={1}>
        <Box display="flex" gap={1} flexWrap="wrap">
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setOpenCreaConCavi(true)}
            color="primary"
          >
            Crea con Cavi
          </Button>

          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog('create')}
          >
            Nuova Comanda
          </Button>

          <Button
            variant="outlined"
            startIcon={<AssignIcon />}
            onClick={() => {
              if (comande.length === 0) {
                setError('Nessuna comanda disponibile per l\'assegnazione');
                return;
              }
              // Apri dialog per selezionare comanda
              setError('Seleziona una comanda dalla tabella e clicca sull\'icona "Assegna Cavi"');
            }}
            disabled={comande.length === 0}
          >
            Assegna Cavi
          </Button>
        </Box>

        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => {
            loadComande();
            loadStatistiche();
          }}
        >
          Aggiorna
        </Button>
      </Box>

      {/* Messaggio di errore */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabella comande */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Codice</TableCell>
              <TableCell>Tipo</TableCell>
              <TableCell>Priorità</TableCell>
              <TableCell>Responsabile</TableCell>
              <TableCell>Data Creazione</TableCell>
              <TableCell>Stato</TableCell>
              <TableCell>Cavi Assegnati</TableCell>
              <TableCell>Completamento</TableCell>
              <TableCell>Azioni</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {comande.map((comanda) => (
              <TableRow key={comanda.codice_comanda}>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {comanda.codice_comanda}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getTipoComandaLabel(comanda.tipo_comanda)}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={comanda.priorita || 'NORMALE'}
                    size="small"
                    color={getPrioritaColor(comanda.priorita || 'NORMALE')}
                  />
                </TableCell>
                <TableCell>{comanda.responsabile}</TableCell>
                <TableCell>
                  {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}
                </TableCell>
                <TableCell>
                  <Chip 
                    label={comanda.stato}
                    color={getStatoColor(comanda.stato)}
                    size="small"
                  />
                </TableCell>
                <TableCell>{comanda.numero_cavi_assegnati || 0}</TableCell>
                <TableCell>
                  {comanda.percentuale_completamento ? 
                    `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'}
                </TableCell>
                <TableCell>
                  <Tooltip title="Visualizza">
                    <IconButton 
                      size="small"
                      onClick={() => handleOpenDialog('view', comanda)}
                    >
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Modifica">
                    <IconButton 
                      size="small"
                      onClick={() => handleOpenDialog('edit', comanda)}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Assegna Cavi">
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog('assign', comanda)}
                      color="primary"
                    >
                      <AssignIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Elimina">
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(comanda.codice_comanda)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {comande.length === 0 && !loading && (
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="textSecondary">
            Nessuna comanda trovata
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Clicca su "Nuova Comanda" per iniziare
          </Typography>
        </Box>
      )}

      {/* Dialog per creazione/modifica/assegnazione */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {dialogMode === 'create' && 'Nuova Comanda'}
          {dialogMode === 'edit' && 'Modifica Comanda'}
          {dialogMode === 'view' && 'Dettagli Comanda'}
          {dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda?.codice_comanda}`}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            {dialogMode === 'assign' ? (
              <>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.
                </Alert>
                <TextField
                  fullWidth
                  label="ID Cavi (separati da virgola)"
                  value={caviAssegnazione}
                  onChange={(e) => setCaviAssegnazione(e.target.value)}
                  margin="normal"
                  placeholder="es: CAVO001, CAVO002, CAVO003"
                  helperText="Esempio: CAVO001, CAVO002, CAVO003"
                  multiline
                  rows={3}
                />
              </>
            ) : dialogMode === 'view' && selectedComanda ? (
              <>
                <List>
                  <ListItem>
                    <ListItemText
                      primary="Codice Comanda"
                      secondary={selectedComanda.codice_comanda}
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Tipo"
                      secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Stato"
                      secondary={
                        <Chip
                          label={selectedComanda.stato}
                          color={getStatoColor(selectedComanda.stato)}
                          size="small"
                        />
                      }
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Descrizione"
                      secondary={selectedComanda.descrizione || 'Nessuna descrizione'}
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Priorità"
                      secondary={
                        <Chip
                          label={selectedComanda.priorita || 'NORMALE'}
                          color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}
                          size="small"
                        />
                      }
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Responsabile"
                      secondary={selectedComanda.responsabile || 'Non assegnato'}
                    />
                  </ListItem>
                  {selectedComanda.note_capo_cantiere && (
                    <>
                      <Divider />
                      <ListItem>
                        <ListItemText
                          primary="Note Capo Cantiere"
                          secondary={selectedComanda.note_capo_cantiere}
                        />
                      </ListItem>
                    </>
                  )}
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Data Creazione"
                      secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}
                    />
                  </ListItem>
                  {selectedComanda.data_scadenza && (
                    <>
                      <Divider />
                      <ListItem>
                        <ListItemText
                          primary="Data Scadenza"
                          secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}
                        />
                      </ListItem>
                    </>
                  )}
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Cavi Assegnati"
                      secondary={selectedComanda.numero_cavi_assegnati || 0}
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary="Completamento"
                      secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}
                    />
                  </ListItem>
                </List>
              </>
            ) : (
              <>
                <TextField
                  fullWidth
                  select
                  label="Tipo Comanda"
                  value={formData.tipo_comanda}
                  onChange={(e) => setFormData({ ...formData, tipo_comanda: e.target.value })}
                  margin="normal"
                  disabled={dialogMode === 'view'}
                >
                  <MenuItem value="POSA">Posa</MenuItem>
                  <MenuItem value="COLLEGAMENTO_PARTENZA">Collegamento Partenza</MenuItem>
                  <MenuItem value="COLLEGAMENTO_ARRIVO">Collegamento Arrivo</MenuItem>
                  <MenuItem value="CERTIFICAZIONE">Certificazione</MenuItem>
                  <MenuItem value="TESTING">Testing</MenuItem>
                </TextField>

                <TextField
                  fullWidth
                  select
                  label="Priorità"
                  value={formData.priorita}
                  onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}
                  margin="normal"
                  disabled={dialogMode === 'view'}
                >
                  <MenuItem value="BASSA">Bassa</MenuItem>
                  <MenuItem value="NORMALE">Normale</MenuItem>
                  <MenuItem value="ALTA">Alta</MenuItem>
                  <MenuItem value="URGENTE">Urgente</MenuItem>
                </TextField>

                <TextField
                  fullWidth
                  label="Descrizione"
                  value={formData.descrizione}
                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                  margin="normal"
                  multiline
                  rows={3}
                  disabled={dialogMode === 'view'}
                />

                <TextField
                  fullWidth
                  label="Responsabile"
                  value={formData.responsabile}
                  onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}
                  margin="normal"
                  disabled={dialogMode === 'view'}
                  required
                  helperText="Chi eseguirà il lavoro (obbligatorio)"
                />

                <TextField
                  fullWidth
                  label="Note Capo Cantiere"
                  value={formData.note_capo_cantiere}
                  onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}
                  margin="normal"
                  multiline
                  rows={2}
                  disabled={dialogMode === 'view'}
                  helperText="Istruzioni specifiche per il responsabile"
                />

                <TextField
                  fullWidth
                  label="Data Scadenza"
                  type="date"
                  value={formData.data_scadenza}
                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}
                  margin="normal"
                  InputLabelProps={{ shrink: true }}
                  disabled={dialogMode === 'view'}
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {dialogMode === 'view' ? 'Chiudi' : 'Annulla'}
          </Button>
          {dialogMode !== 'view' && (
            <Button onClick={handleSubmit} variant="contained">
              {dialogMode === 'create' ? 'Crea' :
               dialogMode === 'edit' ? 'Salva' :
               dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Dialog per creazione comanda con cavi */}
      <CreaComandaConCavi
        cantiereId={cantiereId}
        open={openCreaConCavi}
        onClose={() => setOpenCreaConCavi(false)}
        onSuccess={() => {
          loadComande();
          loadStatistiche();
          setOpenCreaConCavi(false);
        }}
      />
    </Box>
  );
};

export default ComandeList;
