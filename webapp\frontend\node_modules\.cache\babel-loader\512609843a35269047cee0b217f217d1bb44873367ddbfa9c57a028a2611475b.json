{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CaviFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell } from '@mui/material';\nimport FilterableTable from '../common/FilterableTable';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null\n}) => {\n  _s();\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [{\n    field: 'id_cavo',\n    headerName: 'ID Cavo',\n    dataType: 'text',\n    headerStyle: {\n      fontWeight: 'bold'\n    }\n  },\n  // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n  {\n    field: 'sistema',\n    headerName: 'Sistema',\n    dataType: 'text'\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_teorici',\n    headerName: 'Metri Teorici',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n  }, {\n    field: 'metratura_reale',\n    headerName: 'Metri Reali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n  }, {\n    field: 'stato_installazione',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      let color = 'default';\n      if (row.stato_installazione === 'INSTALLATO') color = 'success';else if (row.stato_installazione === 'IN_CORSO') color = 'warning';else if (row.stato_installazione === 'DA_INSTALLARE') color = 'error';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_installazione || 'N/D',\n        size: \"small\",\n        color: color,\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'id_bobina',\n    headerName: 'Bobina',\n    dataType: 'text',\n    renderCell: row => {\n      // Gestione differenziata per null e BOBINA_VUOTA\n      if (row.id_bobina === null) {\n        // Per cavi non posati (id_bobina è null)\n        return '-';\n      } else if (row.id_bobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        return 'BOBINA VUOTA';\n      } else if (!row.id_bobina) {\n        // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n        return '-';\n      }\n\n      // Estrai solo il numero della bobina (parte dopo '_B')\n      const match = row.id_bobina.match(/_B(.+)$/);\n      return match ? match[1] : row.id_bobina;\n    }\n  }, {\n    field: 'timestamp',\n    headerName: 'Data Modifica',\n    dataType: 'date',\n    renderCell: row => formatDate(row.timestamp)\n  }, {\n    field: 'collegamenti',\n    headerName: 'Collegamenti',\n    dataType: 'number',\n    align: 'center',\n    cellStyle: {\n      textAlign: 'center'\n    },\n    renderCell: row => {\n      let color = 'default';\n      if (row.collegamenti === 2) color = 'success';else if (row.collegamenti === 1) color = 'warning';else color = 'error';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.collegamenti,\n        size: \"small\",\n        color: color,\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        backgroundColor: bgColor,\n        '&:hover': {\n          backgroundColor: 'rgba(0, 0, 0, 0.04)'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    // Mostra sempre le statistiche, anche se non ci sono dati\n    const totalCavi = filteredCavi.length;\n    const installati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const inCorso = filteredCavi.filter(c => c.stato_installazione === 'IN_CORSO').length;\n    const daInstallare = filteredCavi.filter(c => c.stato_installazione === 'DA_INSTALLARE').length;\n\n    // Calcoli più dettagliati per il progress\n    const metriTeoriciTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0);\n    const metriInstallati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').reduce((sum, c) => sum + (c.metratura_reale || c.metri_teorici || 0), 0);\n    const metriRimanenti = metriTeoriciTotali - metriInstallati;\n    const percentualeCompletamento = totalCavi ? Math.round(installati / totalCavi * 100) : 0;\n    const percentualeMetri = metriTeoriciTotali ? Math.round(metriInstallati / metriTeoriciTotali * 100) : 0;\n    return {\n      totalCavi,\n      installati,\n      inCorso,\n      daInstallare,\n      metriTeoriciTotali,\n      metriInstallati,\n      metriRimanenti,\n      percentualeCompletamento,\n      percentualeMetri\n    };\n  };\n  const stats = calculateStats();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        p: 3,\n        bgcolor: 'background.paper',\n        borderRadius: 2,\n        boxShadow: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: [\"Avanzamento Lavori\", revisioneCorrente ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [\" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontWeight: 'normal',\n                color: '#666'\n              },\n              children: [\" - Rev. \\\"\", revisioneCorrente, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 18\n            }, this)]\n          }, void 0, true) : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'primary.light',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Completamento Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 700\n            },\n            children: [stats.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'info.light',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Completamento Metri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 700\n            },\n            children: [stats.percentualeMetri, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'success.light',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Cavi Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 700\n            },\n            children: stats.installati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'warning.light',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Cavi in Corso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 700\n            },\n            children: stats.inCorso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'error.light',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Da Installare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 700\n            },\n            children: stats.daInstallare\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'grey.600',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Metri Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 700\n            },\n            children: [stats.metriInstallati.toFixed(0), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'grey.400',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9\n            },\n            children: \"Metri Rimanenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 700\n            },\n            children: [stats.metriRimanenti.toFixed(0), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: cavi,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessun cavo disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviFilterableTable, \"eRBUlmNTTIMtkQjXirbuK4uD11w=\");\n_c = CaviFilterableTable;\nexport default CaviFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"CaviFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "FilterableTable", "formatDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CaviFilterableTable", "cavi", "loading", "onFilteredDataChange", "revisioneCorrente", "_s", "filteredCavi", "setFilteredCavi", "handleFilteredDataChange", "data", "columns", "field", "headerName", "dataType", "headerStyle", "fontWeight", "align", "cellStyle", "textAlign", "renderCell", "row", "metri_te<PERSON>ci", "toFixed", "metratura_reale", "color", "stato_installazione", "label", "size", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id_bobina", "match", "timestamp", "colle<PERSON>nti", "renderRow", "index", "bgColor", "sx", "backgroundColor", "children", "map", "column", "calculateStats", "totalCavi", "length", "installati", "filter", "c", "inCorso", "daInstallare", "metriTeoriciTotali", "reduce", "sum", "metriInstallati", "metriR<PERSON><PERSON><PERSON>", "percentualeCompletamento", "Math", "round", "percentualeMetri", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "display", "justifyContent", "alignItems", "style", "gridTemplateColumns", "gap", "opacity", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CaviFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell } from '@mui/material';\nimport FilterableTable from '../common/FilterableTable';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n */\nconst CaviFilterableTable = ({ cavi = [], loading = false, onFilteredDataChange = null, revisioneCorrente = null }) => {\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n\n\n  // Definizione delle colonne\n  const columns = [\n    {\n      field: 'id_cavo',\n      headerName: 'ID Cavo',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n    {\n      field: 'sistema',\n      headerName: 'Sistema',\n      dataType: 'text'\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_teorici',\n      headerName: 'Metri Teorici',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n    },\n    {\n      field: 'metratura_reale',\n      headerName: 'Metri Reali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_installazione',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.stato_installazione === 'INSTALLATO') color = 'success';\n        else if (row.stato_installazione === 'IN_CORSO') color = 'warning';\n        else if (row.stato_installazione === 'DA_INSTALLARE') color = 'error';\n\n        return (\n          <Chip\n            label={row.stato_installazione || 'N/D'}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    {\n      field: 'id_bobina',\n      headerName: 'Bobina',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Gestione differenziata per null e BOBINA_VUOTA\n        if (row.id_bobina === null) {\n          // Per cavi non posati (id_bobina è null)\n          return '-';\n        } else if (row.id_bobina === 'BOBINA_VUOTA') {\n          // Per cavi posati senza bobina specifica\n          return 'BOBINA VUOTA';\n        } else if (!row.id_bobina) {\n          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n          return '-';\n        }\n\n        // Estrai solo il numero della bobina (parte dopo '_B')\n        const match = row.id_bobina.match(/_B(.+)$/);\n        return match ? match[1] : row.id_bobina;\n      }\n    },\n    {\n      field: 'timestamp',\n      headerName: 'Data Modifica',\n      dataType: 'date',\n      renderCell: (row) => formatDate(row.timestamp)\n    },\n    {\n      field: 'collegamenti',\n      headerName: 'Collegamenti',\n      dataType: 'number',\n      align: 'center',\n      cellStyle: { textAlign: 'center' },\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.collegamenti === 2) color = 'success';\n        else if (row.collegamenti === 1) color = 'warning';\n        else color = 'error';\n\n        return (\n          <Chip\n            label={row.collegamenti}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';\n\n    return (\n      <TableRow\n        key={index}\n        sx={{\n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    // Mostra sempre le statistiche, anche se non ci sono dati\n    const totalCavi = filteredCavi.length;\n    const installati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const inCorso = filteredCavi.filter(c => c.stato_installazione === 'IN_CORSO').length;\n    const daInstallare = filteredCavi.filter(c => c.stato_installazione === 'DA_INSTALLARE').length;\n\n    // Calcoli più dettagliati per il progress\n    const metriTeoriciTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0);\n    const metriInstallati = filteredCavi\n      .filter(c => c.stato_installazione === 'INSTALLATO')\n      .reduce((sum, c) => sum + (c.metratura_reale || c.metri_teorici || 0), 0);\n    const metriRimanenti = metriTeoriciTotali - metriInstallati;\n\n    const percentualeCompletamento = totalCavi ? Math.round((installati / totalCavi) * 100) : 0;\n    const percentualeMetri = metriTeoriciTotali ? Math.round((metriInstallati / metriTeoriciTotali) * 100) : 0;\n\n    return {\n      totalCavi,\n      installati,\n      inCorso,\n      daInstallare,\n      metriTeoriciTotali,\n      metriInstallati,\n      metriRimanenti,\n      percentualeCompletamento,\n      percentualeMetri\n    };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <Box>\n      {/* Statistiche sempre visibili */}\n      <Box sx={{ mb: 3, p: 3, bgcolor: 'background.paper', borderRadius: 2, boxShadow: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            Avanzamento Lavori{revisioneCorrente ? (\n              <> <span style={{ fontWeight: 'normal', color: '#666' }}> - Rev. \"{revisioneCorrente}\"</span></>\n            ) : ''}\n          </Typography>\n        </Box>\n        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 3 }}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>Completamento Cavi</Typography>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>{stats.percentualeCompletamento}%</Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'info.light', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>Completamento Metri</Typography>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>{stats.percentualeMetri}%</Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.light', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>Cavi Installati</Typography>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>{stats.installati}</Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.light', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>Cavi in Corso</Typography>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>{stats.inCorso}</Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'error.light', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>Da Installare</Typography>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>{stats.daInstallare}</Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'grey.600', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>Metri Installati</Typography>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>{stats.metriInstallati.toFixed(0)} m</Typography>\n          </Box>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'grey.400', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>Metri Rimanenti</Typography>\n            <Typography variant=\"h5\" sx={{ fontWeight: 700 }}>{stats.metriRimanenti.toFixed(0)} m</Typography>\n          </Box>\n        </Box>\n      </Box>\n\n      <FilterableTable\n        data={cavi}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        renderRow={renderRow}\n      />\n    </Box>\n  );\n};\n\nexport default CaviFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,eAAe;AAC1E,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,IAAI,GAAG,EAAE;EAAEC,OAAO,GAAG,KAAK;EAAEC,oBAAoB,GAAG,IAAI;EAAEC,iBAAiB,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrH,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAACc,IAAI,CAAC;;EAEtD;EACAb,SAAS,CAAC,MAAM;IACdmB,eAAe,CAACN,IAAI,CAAC;EACvB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMO,wBAAwB,GAAIC,IAAI,IAAK;IACzCF,eAAe,CAACE,IAAI,CAAC;IACrB,IAAIN,oBAAoB,EAAE;MACxBA,oBAAoB,CAACM,IAAI,CAAC;IAC5B;EACF,CAAC;;EAID;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE;MAAEC,UAAU,EAAE;IAAO;EACpC,CAAC;EACD;EACA;IACEJ,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,MAAM;IAChBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACEP,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACC,aAAa,GAAGD,GAAG,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACEX,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACG,eAAe,GAAGH,GAAG,CAACG,eAAe,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC9E,CAAC,EACD;IACEX,KAAK,EAAE,qBAAqB;IAC5BC,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAK;MACnB,IAAII,KAAK,GAAG,SAAS;MACrB,IAAIJ,GAAG,CAACK,mBAAmB,KAAK,YAAY,EAAED,KAAK,GAAG,SAAS,CAAC,KAC3D,IAAIJ,GAAG,CAACK,mBAAmB,KAAK,UAAU,EAAED,KAAK,GAAG,SAAS,CAAC,KAC9D,IAAIJ,GAAG,CAACK,mBAAmB,KAAK,eAAe,EAAED,KAAK,GAAG,OAAO;MAErE,oBACE3B,OAAA,CAACN,IAAI;QACHmC,KAAK,EAAEN,GAAG,CAACK,mBAAmB,IAAI,KAAM;QACxCE,IAAI,EAAC,OAAO;QACZH,KAAK,EAAEA,KAAM;QACbI,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAK;MACnB;MACA,IAAIA,GAAG,CAACa,SAAS,KAAK,IAAI,EAAE;QAC1B;QACA,OAAO,GAAG;MACZ,CAAC,MAAM,IAAIb,GAAG,CAACa,SAAS,KAAK,cAAc,EAAE;QAC3C;QACA,OAAO,cAAc;MACvB,CAAC,MAAM,IAAI,CAACb,GAAG,CAACa,SAAS,EAAE;QACzB;QACA,OAAO,GAAG;MACZ;;MAEA;MACA,MAAMC,KAAK,GAAGd,GAAG,CAACa,SAAS,CAACC,KAAK,CAAC,SAAS,CAAC;MAC5C,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGd,GAAG,CAACa,SAAS;IACzC;EACF,CAAC,EACD;IACEtB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAKzB,UAAU,CAACyB,GAAG,CAACe,SAAS;EAC/C,CAAC,EACD;IACExB,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAC;IAClCC,UAAU,EAAGC,GAAG,IAAK;MACnB,IAAII,KAAK,GAAG,SAAS;MACrB,IAAIJ,GAAG,CAACgB,YAAY,KAAK,CAAC,EAAEZ,KAAK,GAAG,SAAS,CAAC,KACzC,IAAIJ,GAAG,CAACgB,YAAY,KAAK,CAAC,EAAEZ,KAAK,GAAG,SAAS,CAAC,KAC9CA,KAAK,GAAG,OAAO;MAEpB,oBACE3B,OAAA,CAACN,IAAI;QACHmC,KAAK,EAAEN,GAAG,CAACgB,YAAa;QACxBT,IAAI,EAAC,OAAO;QACZH,KAAK,EAAEA,KAAM;QACbI,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,CACF;;EAED;EACA,MAAMK,SAAS,GAAGA,CAACjB,GAAG,EAAEkB,KAAK,KAAK;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,IAAInB,GAAG,CAACK,mBAAmB,KAAK,YAAY,EAAEc,OAAO,GAAG,wBAAwB,CAAC,KAC5E,IAAInB,GAAG,CAACK,mBAAmB,KAAK,UAAU,EAAEc,OAAO,GAAG,wBAAwB;IAEnF,oBACE1C,OAAA,CAACL,QAAQ;MAEPgD,EAAE,EAAE;QACFC,eAAe,EAAEF,OAAO;QACxB,SAAS,EAAE;UAAEE,eAAe,EAAE;QAAsB;MACtD,CAAE;MAAAC,QAAA,EAEDhC,OAAO,CAACiC,GAAG,CAAEC,MAAM,iBAClB/C,OAAA,CAACJ,SAAS;QAERuB,KAAK,EAAE4B,MAAM,CAAC5B,KAAK,IAAI,MAAO;QAC9BwB,EAAE,EAAEI,MAAM,CAAC3B,SAAU;QAAAyB,QAAA,EAEpBE,MAAM,CAACzB,UAAU,GAAGyB,MAAM,CAACzB,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAACwB,MAAM,CAACjC,KAAK;MAAC,GAJ1DiC,MAAM,CAACjC,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GAdGM,KAAK;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeF,CAAC;EAEf,CAAC;;EAED;EACA,MAAMa,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,SAAS,GAAGxC,YAAY,CAACyC,MAAM;IACrC,MAAMC,UAAU,GAAG1C,YAAY,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,mBAAmB,KAAK,YAAY,CAAC,CAACsB,MAAM;IAC1F,MAAMI,OAAO,GAAG7C,YAAY,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,mBAAmB,KAAK,UAAU,CAAC,CAACsB,MAAM;IACrF,MAAMK,YAAY,GAAG9C,YAAY,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,mBAAmB,KAAK,eAAe,CAAC,CAACsB,MAAM;;IAE/F;IACA,MAAMM,kBAAkB,GAAG/C,YAAY,CAACgD,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,IAAIL,CAAC,CAAC7B,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3F,MAAMmC,eAAe,GAAGlD,YAAY,CACjC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,mBAAmB,KAAK,YAAY,CAAC,CACnD6B,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,IAAIL,CAAC,CAAC3B,eAAe,IAAI2B,CAAC,CAAC7B,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3E,MAAMoC,cAAc,GAAGJ,kBAAkB,GAAGG,eAAe;IAE3D,MAAME,wBAAwB,GAAGZ,SAAS,GAAGa,IAAI,CAACC,KAAK,CAAEZ,UAAU,GAAGF,SAAS,GAAI,GAAG,CAAC,GAAG,CAAC;IAC3F,MAAMe,gBAAgB,GAAGR,kBAAkB,GAAGM,IAAI,CAACC,KAAK,CAAEJ,eAAe,GAAGH,kBAAkB,GAAI,GAAG,CAAC,GAAG,CAAC;IAE1G,OAAO;MACLP,SAAS;MACTE,UAAU;MACVG,OAAO;MACPC,YAAY;MACZC,kBAAkB;MAClBG,eAAe;MACfC,cAAc;MACdC,wBAAwB;MACxBG;IACF,CAAC;EACH,CAAC;EAED,MAAMC,KAAK,GAAGjB,cAAc,CAAC,CAAC;EAE9B,oBACEhD,OAAA,CAACR,GAAG;IAAAqD,QAAA,gBAEF7C,OAAA,CAACR,GAAG;MAACmD,EAAE,EAAE;QAAEuB,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE,kBAAkB;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAzB,QAAA,gBACnF7C,OAAA,CAACR,GAAG;QAACmD,EAAE,EAAE;UAAE4B,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEP,EAAE,EAAE;QAAE,CAAE;QAAArB,QAAA,eACzF7C,OAAA,CAACP,UAAU;UAACsC,OAAO,EAAC,IAAI;UAACY,EAAE,EAAE;YAAEzB,UAAU,EAAE;UAAI,CAAE;UAAA2B,QAAA,GAAC,oBAC9B,EAACtC,iBAAiB,gBAClCP,OAAA,CAAAE,SAAA;YAAA2C,QAAA,GAAE,GAAC,eAAA7C,OAAA;cAAM0E,KAAK,EAAE;gBAAExD,UAAU,EAAE,QAAQ;gBAAES,KAAK,EAAE;cAAO,CAAE;cAAAkB,QAAA,GAAC,YAAS,EAACtC,iBAAiB,EAAC,IAAC;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAAE,CAAC,GAC9F,EAAE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNnC,OAAA,CAACR,GAAG;QAACmD,EAAE,EAAE;UAAE4B,OAAO,EAAE,MAAM;UAAEI,mBAAmB,EAAE,sCAAsC;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBAChG7C,OAAA,CAACR,GAAG;UAACmD,EAAE,EAAE;YAAEtB,SAAS,EAAE,QAAQ;YAAE8C,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,eAAe;YAAEC,YAAY,EAAE,CAAC;YAAE1C,KAAK,EAAE;UAAQ,CAAE;UAAAkB,QAAA,gBAChG7C,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAAkB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjFnC,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,IAAI;YAACY,EAAE,EAAE;cAAEzB,UAAU,EAAE;YAAI,CAAE;YAAA2B,QAAA,GAAEoB,KAAK,CAACJ,wBAAwB,EAAC,GAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACNnC,OAAA,CAACR,GAAG;UAACmD,EAAE,EAAE;YAAEtB,SAAS,EAAE,QAAQ;YAAE8C,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,YAAY;YAAEC,YAAY,EAAE,CAAC;YAAE1C,KAAK,EAAE;UAAQ,CAAE;UAAAkB,QAAA,gBAC7F7C,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAAmB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClFnC,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,IAAI;YAACY,EAAE,EAAE;cAAEzB,UAAU,EAAE;YAAI,CAAE;YAAA2B,QAAA,GAAEoB,KAAK,CAACD,gBAAgB,EAAC,GAAC;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eACNnC,OAAA,CAACR,GAAG;UAACmD,EAAE,EAAE;YAAEtB,SAAS,EAAE,QAAQ;YAAE8C,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,eAAe;YAAEC,YAAY,EAAE,CAAC;YAAE1C,KAAK,EAAE;UAAQ,CAAE;UAAAkB,QAAA,gBAChG7C,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAAe;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9EnC,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,IAAI;YAACY,EAAE,EAAE;cAAEzB,UAAU,EAAE;YAAI,CAAE;YAAA2B,QAAA,EAAEoB,KAAK,CAACd;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNnC,OAAA,CAACR,GAAG;UAACmD,EAAE,EAAE;YAAEtB,SAAS,EAAE,QAAQ;YAAE8C,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,eAAe;YAAEC,YAAY,EAAE,CAAC;YAAE1C,KAAK,EAAE;UAAQ,CAAE;UAAAkB,QAAA,gBAChG7C,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5EnC,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,IAAI;YAACY,EAAE,EAAE;cAAEzB,UAAU,EAAE;YAAI,CAAE;YAAA2B,QAAA,EAAEoB,KAAK,CAACX;UAAO;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACNnC,OAAA,CAACR,GAAG;UAACmD,EAAE,EAAE;YAAEtB,SAAS,EAAE,QAAQ;YAAE8C,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,aAAa;YAAEC,YAAY,EAAE,CAAC;YAAE1C,KAAK,EAAE;UAAQ,CAAE;UAAAkB,QAAA,gBAC9F7C,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5EnC,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,IAAI;YAACY,EAAE,EAAE;cAAEzB,UAAU,EAAE;YAAI,CAAE;YAAA2B,QAAA,EAAEoB,KAAK,CAACV;UAAY;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACNnC,OAAA,CAACR,GAAG;UAACmD,EAAE,EAAE;YAAEtB,SAAS,EAAE,QAAQ;YAAE8C,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,UAAU;YAAEC,YAAY,EAAE,CAAC;YAAE1C,KAAK,EAAE;UAAQ,CAAE;UAAAkB,QAAA,gBAC3F7C,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAAgB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/EnC,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,IAAI;YAACY,EAAE,EAAE;cAAEzB,UAAU,EAAE;YAAI,CAAE;YAAA2B,QAAA,GAAEoB,KAAK,CAACN,eAAe,CAAClC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACNnC,OAAA,CAACR,GAAG;UAACmD,EAAE,EAAE;YAAEtB,SAAS,EAAE,QAAQ;YAAE8C,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,UAAU;YAAEC,YAAY,EAAE,CAAC;YAAE1C,KAAK,EAAE;UAAQ,CAAE;UAAAkB,QAAA,gBAC3F7C,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEkC,OAAO,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAAe;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9EnC,OAAA,CAACP,UAAU;YAACsC,OAAO,EAAC,IAAI;YAACY,EAAE,EAAE;cAAEzB,UAAU,EAAE;YAAI,CAAE;YAAA2B,QAAA,GAAEoB,KAAK,CAACL,cAAc,CAACnC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnC,OAAA,CAACH,eAAe;MACde,IAAI,EAAER,IAAK;MACXS,OAAO,EAAEA,OAAQ;MACjBP,oBAAoB,EAAEK,wBAAyB;MAC/CN,OAAO,EAAEA,OAAQ;MACjByE,YAAY,EAAC,yBAAyB;MACtCtC,SAAS,EAAEA;IAAU;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA5PIL,mBAAmB;AAAA4E,EAAA,GAAnB5E,mBAAmB;AA8PzB,eAAeA,mBAAmB;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}