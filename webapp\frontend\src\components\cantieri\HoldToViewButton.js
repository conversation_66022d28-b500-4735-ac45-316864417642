import React, { useState, useRef, useCallback } from 'react';
import {
  Button,
  Box,
  Typography,
  LinearProgress,
  Tooltip
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Lock as LockIcon
} from '@mui/icons-material';

/**
 * Componente pulsante "Tieni premuto per visualizzare"
 * Richiede di tenere premuto il pulsante per 2-3 secondi prima di eseguire l'azione
 */
const HoldToViewButton = ({
  onComplete,
  loading = false,
  disabled = false,
  holdDuration = null, // Se null, usa un timer casuale
  children = "Tieni premuto per visualizzare",
  variant = "outlined",
  color = "primary",
  size = "medium",
  fullWidth = false,
  startIcon = <VisibilityIcon />
}) => {
  const [isHolding, setIsHolding] = useState(false);
  const [progress, setProgress] = useState(0);
  const [completed, setCompleted] = useState(false);
  const [currentHoldDuration, setCurrentHoldDuration] = useState(holdDuration);

  const holdTimerRef = useRef(null);
  const progressTimerRef = useRef(null);
  const startTimeRef = useRef(null);

  // Pulisce i timer
  const clearTimers = useCallback(() => {
    if (holdTimerRef.current) {
      clearTimeout(holdTimerRef.current);
      holdTimerRef.current = null;
    }
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }
  }, []);

  // Inizia il hold
  const startHold = useCallback(() => {
    if (disabled || loading || completed) return;

    // Genera un timer casuale se non specificato
    const duration = holdDuration || (Math.random() * 3000 + 2000); // 2-5 secondi
    setCurrentHoldDuration(duration);

    setIsHolding(true);
    setProgress(0);
    startTimeRef.current = Date.now();

    console.log(`🔒 Timer casuale generato: ${(duration / 1000).toFixed(1)} secondi`);

    // Timer per il progresso
    progressTimerRef.current = setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      const newProgress = Math.min((elapsed / duration) * 100, 100);
      setProgress(newProgress);
    }, 50); // Aggiorna ogni 50ms per fluidità

    // Timer per il completamento
    holdTimerRef.current = setTimeout(() => {
      setCompleted(true);
      setIsHolding(false);
      setProgress(100);
      clearTimers();

      console.log(`✅ Timer completato dopo ${(duration / 1000).toFixed(1)} secondi`);

      // Esegui l'azione
      if (onComplete) {
        onComplete();
      }

      // Reset dopo un breve delay
      setTimeout(() => {
        setCompleted(false);
        setProgress(0);
      }, 1000);
    }, duration);
  }, [disabled, loading, completed, holdDuration, onComplete, clearTimers]);

  // Ferma il hold
  const stopHold = useCallback(() => {
    if (completed) return;
    
    setIsHolding(false);
    setProgress(0);
    clearTimers();
  }, [completed, clearTimers]);

  // Gestisce mouse events
  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    startHold();
  }, [startHold]);

  const handleMouseUp = useCallback(() => {
    stopHold();
  }, [stopHold]);

  const handleMouseLeave = useCallback(() => {
    stopHold();
  }, [stopHold]);

  // Gestisce touch events per mobile
  const handleTouchStart = useCallback((e) => {
    e.preventDefault();
    startHold();
  }, [startHold]);

  const handleTouchEnd = useCallback(() => {
    stopHold();
  }, [stopHold]);

  // Previene il context menu
  const handleContextMenu = useCallback((e) => {
    e.preventDefault();
  }, []);

  // Cleanup al unmount
  React.useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  // Determina il colore del pulsante
  const getButtonColor = () => {
    if (completed) return 'success';
    if (isHolding) return 'warning';
    return color;
  };

  // Determina l'icona
  const getIcon = () => {
    if (completed) return <VisibilityIcon />;
    if (isHolding) return <LockIcon />;
    return startIcon;
  };

  // Determina il testo
  const getText = () => {
    if (completed) return "Password visualizzata!";
    if (isHolding) return "Continua a tenere premuto...";
    return children;
  };

  return (
    <Tooltip
      title={disabled ? "Funzione non disponibile" : "Tieni premuto per 2-5 secondi per visualizzare la password (timer casuale anti-bot)"}
      arrow
    >
      <Box sx={{ position: 'relative', width: fullWidth ? '100%' : 'auto' }}>
        <Button
          variant={variant}
          color={getButtonColor()}
          size={size}
          fullWidth={fullWidth}
          disabled={disabled || loading}
          startIcon={getIcon()}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
          onContextMenu={handleContextMenu}
          sx={{
            position: 'relative',
            overflow: 'hidden',
            userSelect: 'none',
            transition: 'all 0.2s ease',
            '&:active': {
              transform: 'scale(0.98)'
            },
            ...(isHolding && {
              boxShadow: '0 0 10px rgba(255, 152, 0, 0.5)'
            }),
            ...(completed && {
              boxShadow: '0 0 10px rgba(76, 175, 80, 0.5)'
            })
          }}
        >
          <Typography variant="button" sx={{ position: 'relative', zIndex: 2 }}>
            {getText()}
          </Typography>
          
          {/* Barra di progresso */}
          {(isHolding || completed) && (
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: 4,
                zIndex: 1,
                '& .MuiLinearProgress-bar': {
                  backgroundColor: completed ? 'success.main' : 'warning.main',
                  transition: 'none'
                }
              }}
            />
          )}
        </Button>
        
        {/* Indicatore di progresso circolare per mobile */}
        {isHolding && (
          <Box
            sx={{
              position: 'absolute',
              top: -2,
              right: -2,
              width: 20,
              height: 20,
              borderRadius: '50%',
              background: `conic-gradient(orange ${progress * 3.6}deg, transparent 0deg)`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              '&::before': {
                content: '""',
                width: 14,
                height: 14,
                borderRadius: '50%',
                backgroundColor: 'background.paper'
              }
            }}
          />
        )}
      </Box>
    </Tooltip>
  );
};

export default HoldToViewButton;
