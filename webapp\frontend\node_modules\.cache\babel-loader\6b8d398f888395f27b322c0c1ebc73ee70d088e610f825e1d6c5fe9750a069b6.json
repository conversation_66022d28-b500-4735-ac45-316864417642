{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\nimport TopNavbar from '../components/TopNavbar';\nimport Footer from '../components/Footer';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioneCEI64_8Page from './cavi/CertificazioneCEI64_8Page';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\nimport StoricoUtilizzoPage from './cavi/parco/StoricoUtilizzoPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\nimport PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\n\n// Importa le pagine per le comande\nimport ComandePage from './comande/ComandePage';\nimport TestComande from '../components/comande/TestComande';\nimport AccessoRapidoComanda from '../components/comande/AccessoRapidoComanda';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(UserExpirationChecker, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TopNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        // Sfondo grigio chiaro per l'area principale\n        minHeight: 'calc(100vh - 40px)',\n        // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n        overflowX: 'hidden',\n        // Previene scrollbar orizzontale\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1,\n          mb: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin\",\n            element: /*#__PURE__*/_jsxDEV(AdminPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cantieri\",\n            element: /*#__PURE__*/_jsxDEV(UserPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cantieri/:cantiereId\",\n            element: /*#__PURE__*/_jsxDEV(CantierePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cantieri/:cantiereId/certificazioni\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioniPageDebug, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 73\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi\",\n            element: /*#__PURE__*/_jsxDEV(CaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/visualizza\",\n            element: /*#__PURE__*/_jsxDEV(VisualizzaCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/posa\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard/cavi/visualizza\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/parco\",\n            element: /*#__PURE__*/_jsxDEV(ParcoCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/excel\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard/cavi/visualizza\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/report\",\n            element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/:cantiereId/report\",\n            element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 61\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 57\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/visualizza\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 68\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/filtra\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 64\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/crea\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 62\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/modifica\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/dettagli\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/pdf\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 61\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/elimina\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 65\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione/strumenti\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 67\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione-cei/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 71\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione-cei/rapporti\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 70\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione-cei/prove\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 67\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione-cei/non-conformita\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 76\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/certificazione-cei/strumenti\",\n            element: /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8Page, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 71\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/comande\",\n            element: /*#__PURE__*/_jsxDEV(GestioneComandeePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cantieri/:cantiereId/comande\",\n            element: /*#__PURE__*/_jsxDEV(ComandePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/test/comande\",\n            element: /*#__PURE__*/_jsxDEV(TestComande, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/accesso-rapido-comanda\",\n            element: /*#__PURE__*/_jsxDEV(AccessoRapidoComanda, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/test\",\n            element: /*#__PURE__*/_jsxDEV(TestCaviPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/test-bobine/:cantiereId\",\n            element: /*#__PURE__*/_jsxDEV(TestBobinePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/parco/visualizza\",\n            element: /*#__PURE__*/_jsxDEV(VisualizzaBobinePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/parco/crea\",\n            element: /*#__PURE__*/_jsxDEV(CreaBobinaPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/parco/modifica\",\n            element: /*#__PURE__*/_jsxDEV(ParcoCaviModificaBobinaPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 57\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/parco/elimina\",\n            element: /*#__PURE__*/_jsxDEV(EliminaBobinaPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/parco/storico\",\n            element: /*#__PURE__*/_jsxDEV(StoricoUtilizzoPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/posa/inserisci-metri\",\n            element: /*#__PURE__*/_jsxDEV(InserisciMetriPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 63\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/posa/metri-posati-semplificato\",\n            element: /*#__PURE__*/_jsxDEV(MetriPosatiSemplificatoPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 73\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/posa/modifica-cavo\",\n            element: /*#__PURE__*/_jsxDEV(ModificaCavoPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 61\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cantieri/:cantiereId/cavi/posa/modifica-cavo\",\n            element: /*#__PURE__*/_jsxDEV(ModificaCavoPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 82\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/posa/modifica-bobina\",\n            element: /*#__PURE__*/_jsxDEV(PosaCaviModificaBobinaPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 63\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?\",\n            element: /*#__PURE__*/_jsxDEV(PosaCaviModificaBobinaPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 93\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cavi/posa/collegamenti\",\n            element: /*#__PURE__*/_jsxDEV(CollegamentiPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cantieri/:cantiereId/cavi/posa/collegamenti\",\n            element: /*#__PURE__*/_jsxDEV(CollegamentiPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 81\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "Box", "CssBaseline", "TopNavbar", "Footer", "HomePage", "AdminPage", "UserPage", "CaviPage", "UserExpirationChecker", "VisualizzaCaviPage", "ParcoCaviPage", "ReportCaviPageNew", "CertificazioneCaviPage", "CertificazioneCEI64_8Page", "CertificazioniPageDebug", "GestioneComandeePage", "TestCaviPage", "TestBobinePage", "VisualizzaBobinePage", "CreaBobinaPage", "ParcoCaviModificaBobinaPage", "EliminaBobinaPage", "StoricoUtilizzoPage", "InserisciMetriPage", "MetriPosatiSemplificatoPage", "ModificaCavoPage", "PosaCaviModificaBobinaPage", "CollegamentiPage", "CantierePage", "ComandePage", "TestComande", "AccessoRapidoComanda", "jsxDEV", "_jsxDEV", "Dashboard", "sx", "display", "flexDirection", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "flexGrow", "p", "width", "backgroundColor", "minHeight", "overflowX", "mb", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\n\n\nimport TopNavbar from '../components/TopNavbar';\nimport Footer from '../components/Footer';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\n\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioneCEI64_8Page from './cavi/CertificazioneCEI64_8Page';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\nimport StoricoUtilizzoPage from './cavi/parco/StoricoUtilizzoPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\n\nimport PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\n\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\n\n// Importa le pagine per le comande\nimport ComandePage from './comande/ComandePage';\nimport TestComande from '../components/comande/TestComande';\nimport AccessoRapidoComanda from '../components/comande/AccessoRapidoComanda';\n\nconst Dashboard = () => {\n\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column' }}>\n      {/* Componente invisibile che verifica gli utenti scaduti */}\n      <UserExpirationChecker />\n      <CssBaseline />\n      <TopNavbar />\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: '100%',\n          backgroundColor: '#f5f5f5', // Sfondo grigio chiaro per l'area principale\n          minHeight: 'calc(100vh - 40px)', // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n          overflowX: 'hidden', // Previene scrollbar orizzontale\n          display: 'flex',\n          flexDirection: 'column'\n        }}\n      >\n        <Box sx={{ flexGrow: 1, mb: 4 }}>\n          <Routes>\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/admin\" element={<AdminPage />} />\n            <Route path=\"/cantieri\" element={<UserPage />} />\n            <Route path=\"/cantieri/:cantiereId\" element={<CantierePage />} />\n            <Route path=\"/cantieri/:cantiereId/certificazioni\" element={<CertificazioniPageDebug />} />\n\n            {/* Route per la gestione cavi */}\n            <Route path=\"/cavi\" element={<CaviPage />} />\n            <Route path=\"/cavi/visualizza\" element={<VisualizzaCaviPage />} />\n            <Route path=\"/cavi/posa\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n            <Route path=\"/cavi/parco\" element={<ParcoCaviPage />} />\n            <Route path=\"/cavi/excel\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n            <Route path=\"/cavi/report\" element={<ReportCaviPageNew />} />\n            <Route path=\"/cavi/:cantiereId/report\" element={<ReportCaviPageNew />} />\n\n            <Route path=\"/cavi/certificazione\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/visualizza\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/filtra\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/crea\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/modifica\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/dettagli\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/pdf\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/elimina\" element={<CertificazioneCaviPage />} />\n            <Route path=\"/cavi/certificazione/strumenti\" element={<CertificazioneCaviPage />} />\n\n            {/* Route per Certificazione CEI 64-8 */}\n            <Route path=\"/cavi/certificazione-cei/dashboard\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/rapporti\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/prove\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/non-conformita\" element={<CertificazioneCEI64_8Page />} />\n            <Route path=\"/cavi/certificazione-cei/strumenti\" element={<CertificazioneCEI64_8Page />} />\n\n            <Route path=\"/cavi/comande\" element={<GestioneComandeePage />} />\n            <Route path=\"/cantieri/:cantiereId/comande\" element={<ComandePage />} />\n            <Route path=\"/test/comande\" element={<TestComande />} />\n            <Route path=\"/accesso-rapido-comanda\" element={<AccessoRapidoComanda />} />\n            <Route path=\"/cavi/test\" element={<TestCaviPage />} />\n\n            {/* Route per la pagina di test delle bobine */}\n            <Route path=\"/cavi/test-bobine/:cantiereId\" element={<TestBobinePage />} />\n\n            {/* Route per Parco Cavi */}\n            <Route path=\"/cavi/parco/visualizza\" element={<VisualizzaBobinePage />} />\n            <Route path=\"/cavi/parco/crea\" element={<CreaBobinaPage />} />\n            <Route path=\"/cavi/parco/modifica\" element={<ParcoCaviModificaBobinaPage />} />\n            <Route path=\"/cavi/parco/elimina\" element={<EliminaBobinaPage />} />\n            <Route path=\"/cavi/parco/storico\" element={<StoricoUtilizzoPage />} />\n\n            {/* Route per Posa e Collegamenti */}\n            <Route path=\"/cavi/posa/inserisci-metri\" element={<InserisciMetriPage />} />\n            <Route path=\"/cavi/posa/metri-posati-semplificato\" element={<MetriPosatiSemplificatoPage />} />\n            {/* Modifica cavo ora ha una pagina dedicata */}\n            <Route path=\"/cavi/posa/modifica-cavo\" element={<ModificaCavoPage />} />\n            <Route path=\"/cantieri/:cantiereId/cavi/posa/modifica-cavo\" element={<ModificaCavoPage />} />\n\n            <Route path=\"/cavi/posa/modifica-bobina\" element={<PosaCaviModificaBobinaPage />} />\n            <Route path=\"/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?\" element={<PosaCaviModificaBobinaPage />} />\n            <Route path=\"/cavi/posa/collegamenti\" element={<CollegamentiPage />} />\n            <Route path=\"/cantieri/:cantiereId/cavi/posa/collegamenti\" element={<CollegamentiPage />} />\n\n            {/* Altre route verranno aggiunte man mano che vengono implementate */}\n          </Routes>\n        </Box>\n\n        {/* Footer */}\n        <Footer />\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,GAAG,EAAEC,WAAW,QAAQ,eAAe;AAGhD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,qBAAqB,MAAM,2CAA2C;;AAE7E;AACA,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,aAAa,MAAM,sBAAsB;AAEhD,OAAOC,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,sBAAsB,MAAM,+BAA+B;AAClE,OAAOC,yBAAyB,MAAM,kCAAkC;AACxE,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,oBAAoB,MAAM,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qBAAqB;AAC9C,OAAOC,cAAc,MAAM,kBAAkB;;AAE7C;AACA,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,2BAA2B,MAAM,iCAAiC;AACzE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,2BAA2B,MAAM,yCAAyC;AACjF,OAAOC,gBAAgB,MAAM,8BAA8B;AAE3D,OAAOC,0BAA0B,MAAM,gCAAgC;AACvE,OAAOC,gBAAgB,MAAM,8BAA8B;;AAG3D;AACA,OAAOC,YAAY,MAAM,yBAAyB;;AAElD;AACA,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,WAAW,MAAM,mCAAmC;AAC3D,OAAOC,oBAAoB,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAEtB,oBACED,OAAA,CAACjC,GAAG;IAACmC,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpDL,OAAA,CAACzB,qBAAqB;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzBT,OAAA,CAAChC,WAAW;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfT,OAAA,CAAC/B,SAAS;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbT,OAAA,CAACjC,GAAG;MACF2C,SAAS,EAAC,MAAM;MAChBR,EAAE,EAAE;QACFS,QAAQ,EAAE,CAAC;QACXC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE,MAAM;QACbC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,oBAAoB;QAAE;QACjCC,SAAS,EAAE,QAAQ;QAAE;QACrBb,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE;MACjB,CAAE;MAAAC,QAAA,gBAEFL,OAAA,CAACjC,GAAG;QAACmC,EAAE,EAAE;UAAES,QAAQ,EAAE,CAAC;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,eAC9BL,OAAA,CAACpC,MAAM;UAAAyC,QAAA,gBACLL,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEnB,OAAA,CAAC7B,QAAQ;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEnB,OAAA,CAAC5B,SAAS;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEnB,OAAA,CAAC3B,QAAQ;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,uBAAuB;YAACC,OAAO,eAAEnB,OAAA,CAACL,YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,sCAAsC;YAACC,OAAO,eAAEnB,OAAA,CAACnB,uBAAuB;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG3FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEnB,OAAA,CAAC1B,QAAQ;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEnB,OAAA,CAACxB,kBAAkB;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEnB,OAAA,CAAClC,QAAQ;cAACsD,EAAE,EAAC,4BAA4B;cAACC,OAAO;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,aAAa;YAACC,OAAO,eAAEnB,OAAA,CAACvB,aAAa;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,aAAa;YAACC,OAAO,eAAEnB,OAAA,CAAClC,QAAQ;cAACsD,EAAE,EAAC,4BAA4B;cAACC,OAAO;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,cAAc;YAACC,OAAO,eAAEnB,OAAA,CAACtB,iBAAiB;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,0BAA0B;YAACC,OAAO,eAAEnB,OAAA,CAACtB,iBAAiB;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEzET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,sBAAsB;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,iCAAiC;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,6BAA6B;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,2BAA2B;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,+BAA+B;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,+BAA+B;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,0BAA0B;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,8BAA8B;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,gCAAgC;YAACC,OAAO,eAAEnB,OAAA,CAACrB,sBAAsB;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGpFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,oCAAoC;YAACC,OAAO,eAAEnB,OAAA,CAACpB,yBAAyB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,mCAAmC;YAACC,OAAO,eAAEnB,OAAA,CAACpB,yBAAyB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,gCAAgC;YAACC,OAAO,eAAEnB,OAAA,CAACpB,yBAAyB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,yCAAyC;YAACC,OAAO,eAAEnB,OAAA,CAACpB,yBAAyB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChGT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,oCAAoC;YAACC,OAAO,eAAEnB,OAAA,CAACpB,yBAAyB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE3FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEnB,OAAA,CAAClB,oBAAoB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,+BAA+B;YAACC,OAAO,eAAEnB,OAAA,CAACJ,WAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEnB,OAAA,CAACH,WAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,yBAAyB;YAACC,OAAO,eAAEnB,OAAA,CAACF,oBAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEnB,OAAA,CAACjB,YAAY;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGtDT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,+BAA+B;YAACC,OAAO,eAAEnB,OAAA,CAAChB,cAAc;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG3ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAEnB,OAAA,CAACf,oBAAoB;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEnB,OAAA,CAACd,cAAc;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,sBAAsB;YAACC,OAAO,eAAEnB,OAAA,CAACb,2BAA2B;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,qBAAqB;YAACC,OAAO,eAAEnB,OAAA,CAACZ,iBAAiB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,qBAAqB;YAACC,OAAO,eAAEnB,OAAA,CAACX,mBAAmB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGtET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAEnB,OAAA,CAACV,kBAAkB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5ET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,sCAAsC;YAACC,OAAO,eAAEnB,OAAA,CAACT,2BAA2B;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE/FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,0BAA0B;YAACC,OAAO,eAAEnB,OAAA,CAACR,gBAAgB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,+CAA+C;YAACC,OAAO,eAAEnB,OAAA,CAACR,gBAAgB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE7FT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAEnB,OAAA,CAACP,0BAA0B;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,0DAA0D;YAACC,OAAO,eAAEnB,OAAA,CAACP,0BAA0B;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClHT,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,yBAAyB;YAACC,OAAO,eAAEnB,OAAA,CAACN,gBAAgB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvET,OAAA,CAACnC,KAAK;YAACqD,IAAI,EAAC,8CAA8C;YAACC,OAAO,eAAEnB,OAAA,CAACN,gBAAgB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGtF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNT,OAAA,CAAC9B,MAAM;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,GA5FIrB,SAAS;AA8Ff,eAAeA,SAAS;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}