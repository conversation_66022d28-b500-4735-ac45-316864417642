{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\Logo.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Logo = ({\n  width = 40,\n  height = 40,\n  color = '#1976d2'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"svg\", {\n    width: width,\n    height: height,\n    viewBox: \"0 0 100 100\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n      cx: \"50\",\n      cy: \"50\",\n      r: \"45\",\n      stroke: color,\n      strokeWidth: \"6\",\n      fill: \"none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M50 5 L50 30 M50 70 L50 95\",\n      stroke: color,\n      strokeWidth: \"6\",\n      strokeLinecap: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M5 50 L30 50 M70 50 L95 50\",\n      stroke: color,\n      strokeWidth: \"6\",\n      strokeLinecap: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M55 30 L40 50 L55 50 L45 70\",\n      stroke: color,\n      strokeWidth: \"6\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      fill: \"none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Logo;\nexport default Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Logo", "width", "height", "color", "viewBox", "fill", "xmlns", "children", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "strokeLinecap", "strokeLinejoin", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/Logo.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Logo = ({ width = 40, height = 40, color = '#1976d2' }) => {\r\n  return (\r\n    <svg\r\n      width={width}\r\n      height={height}\r\n      viewBox=\"0 0 100 100\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      {/* Cable coil shape */}\r\n      <circle cx=\"50\" cy=\"50\" r=\"45\" stroke={color} strokeWidth=\"6\" fill=\"none\" />\r\n      <path\r\n        d=\"M50 5 L50 30 M50 70 L50 95\"\r\n        stroke={color}\r\n        strokeWidth=\"6\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      <path\r\n        d=\"M5 50 L30 50 M70 50 L95 50\"\r\n        stroke={color}\r\n        strokeWidth=\"6\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n      \r\n      {/* Lightning bolt in the center */}\r\n      <path\r\n        d=\"M55 30 L40 50 L55 50 L45 70\"\r\n        stroke={color}\r\n        strokeWidth=\"6\"\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n        fill=\"none\"\r\n      />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default Logo;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAC;EAAEC,KAAK,GAAG,EAAE;EAAEC,MAAM,GAAG,EAAE;EAAEC,KAAK,GAAG;AAAU,CAAC,KAAK;EAC/D,oBACEJ,OAAA;IACEE,KAAK,EAAEA,KAAM;IACbC,MAAM,EAAEA,MAAO;IACfE,OAAO,EAAC,aAAa;IACrBC,IAAI,EAAC,MAAM;IACXC,KAAK,EAAC,4BAA4B;IAAAC,QAAA,gBAGlCR,OAAA;MAAQS,EAAE,EAAC,IAAI;MAACC,EAAE,EAAC,IAAI;MAACC,CAAC,EAAC,IAAI;MAACC,MAAM,EAAER,KAAM;MAACS,WAAW,EAAC,GAAG;MAACP,IAAI,EAAC;IAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5EjB,OAAA;MACEkB,CAAC,EAAC,4BAA4B;MAC9BN,MAAM,EAAER,KAAM;MACdS,WAAW,EAAC,GAAG;MACfM,aAAa,EAAC;IAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eACFjB,OAAA;MACEkB,CAAC,EAAC,4BAA4B;MAC9BN,MAAM,EAAER,KAAM;MACdS,WAAW,EAAC,GAAG;MACfM,aAAa,EAAC;IAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAGFjB,OAAA;MACEkB,CAAC,EAAC,6BAA6B;MAC/BN,MAAM,EAAER,KAAM;MACdS,WAAW,EAAC,GAAG;MACfM,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC,OAAO;MACtBd,IAAI,EAAC;IAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACI,EAAA,GAnCIpB,IAAI;AAqCV,eAAeA,IAAI;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}