<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modulo Comande di CABLYS - Sistema di gestione delle comande per l'assegnazione e il monitoraggio dei lavori">
    <meta name="keywords" content="CABLYS, comande, gestione lavori, assegnazione cavi, workflow cantieri">
    <meta name="author" content="CABLYS">
    <title>Comande - CABLYS</title>
    <link rel="icon" href="../images/favicon.svg" type="image/svg+xml">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="moduli.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo-container">
                <a href="../index.html">
                    <img src="../images/logo.svg" alt="CABLYS Logo" class="logo">
                    <h1>CABLYS</h1>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="../index.html#servizi">Servizi</a></li>
                    <li><a href="../index.html#moduli">Moduli</a></li>
                    <li><a href="../index.html#chi-siamo">Chi Siamo</a></li>
                    <li><a href="../index.html#contatti">Contatti</a></li>
                    <li><a href="http://localhost:3000/login" class="btn-login">Accedi</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="module-hero">
        <div class="container">
            <div class="module-hero-content">
                <div class="module-icon">
                    <img src="../images/icon-orders.svg" alt="Comande">
                </div>
                <h1>Comande</h1>
                <p>Sistema di gestione delle comande per l'assegnazione e il monitoraggio dei lavori</p>
            </div>
        </div>
    </div>

    <section class="module-overview">
        <div class="container">
            <div class="module-section">
                <h2>Panoramica</h2>
                <p>Il modulo Comande di CABLYS gestisce il workflow completo delle operazioni sui cavi: dalla creazione delle comande con ID univoci e prefissi specifici (POS/CPT/CAR), all'assegnazione dei cavi con controllo dei conflitti, fino al tracciamento dello stato di avanzamento. Il sistema supporta quattro tipi di comande: POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO e CERTIFICAZIONE.</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <h3>Workflow Completo</h3>
                        <p>Gestione del ciclo completo delle comande dalla creazione all'esecuzione con tracciamento dello stato di avanzamento.</p>
                    </div>
                    <div class="feature-card">
                        <h3>Quattro Tipi di Comande</h3>
                        <p>Supporto per POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO e CERTIFICAZIONE con workflow specifici.</p>
                    </div>
                    <div class="feature-card">
                        <h3>Controllo Conflitti</h3>
                        <p>Sistema automatico di verifica dei conflitti nell'assegnazione dei cavi per evitare sovrapposizioni.</p>
                    </div>
                    <div class="feature-card">
                        <h3>Tracciamento Avanzato</h3>
                        <p>Monitoraggio dettagliato dello stato di ogni comanda con aggiornamenti in tempo reale.</p>
                    </div>
                </div>
            </div>

            <div class="module-section">
                <h2>Funzionalità Principali</h2>
                
                <div class="functionality">
                    <div class="functionality-content">
                        <h3>Creazione e Gestione Comande</h3>
                        <p>Creazione di comande con ID univoci e prefissi specifici per ogni tipo di operazione. Il sistema assegna automaticamente i prefissi POS per posa, CPT per collegamenti partenza, CAR per collegamenti arrivo, garantendo identificazione immediata del tipo di lavoro.</p>
                        <ul>
                            <li>ID univoci con prefissi automatici (POS/CPT/CAR)</li>
                            <li>Gestione di quattro tipi di comande specifiche</li>
                            <li>Assegnazione responsabili e team di lavoro</li>
                            <li>Pianificazione date e priorità</li>
                        </ul>
                    </div>
                    <div class="functionality-image">
                        <img src="../images/moduli/comande-creazione.svg" alt="Creazione e gestione comande">
                    </div>
                </div>
                
                <div class="functionality reverse">
                    <div class="functionality-content">
                        <h3>Assegnazione Cavi Intelligente</h3>
                        <p>Sistema di assegnazione cavi con workflow ottimizzato: prima selezione dei cavi, poi scelta del tipo di comanda e assegnazione. Include controllo automatico dei conflitti per evitare assegnazioni multiple dello stesso cavo.</p>
                        <ul>
                            <li>Workflow ottimizzato: selezione cavi → tipo comanda → assegnazione</li>
                            <li>Controllo automatico conflitti e sovrapposizioni</li>
                            <li>Verifica prerequisiti per ogni tipo di comanda</li>
                            <li>Suggerimenti intelligenti per l'assegnazione</li>
                        </ul>
                    </div>
                    <div class="functionality-image">
                        <img src="../images/moduli/comande-assegnazione.svg" alt="Assegnazione cavi intelligente">
                    </div>
                </div>
                
                <div class="functionality">
                    <div class="functionality-content">
                        <h3>Monitoraggio Stato Avanzamento</h3>
                        <p>Tracciamento dettagliato dello stato di ogni comanda attraverso le fasi: CREATA, ASSEGNATA, IN_CORSO, COMPLETATA, ANNULLATA. Dashboard in tempo reale per il monitoraggio delle performance e identificazione di eventuali ritardi.</p>
                        <ul>
                            <li>Stati predefiniti per tracciamento completo</li>
                            <li>Dashboard in tempo reale con KPI</li>
                            <li>Notifiche automatiche per cambi di stato</li>
                            <li>Analisi performance e identificazione ritardi</li>
                        </ul>
                    </div>
                    <div class="functionality-image">
                        <img src="../images/moduli/comande-monitoraggio.svg" alt="Monitoraggio stato avanzamento">
                    </div>
                </div>
                
                <div class="functionality reverse">
                    <div class="functionality-content">
                        <h3>Gestione Tipi di Comande</h3>
                        <p>Supporto specializzato per quattro tipi di comande con workflow e controlli specifici: POSA per installazione cavi, COLLEGAMENTO_PARTENZA e COLLEGAMENTO_ARRIVO per connessioni, CERTIFICAZIONE per test e collaudi.</p>
                        <ul>
                            <li>POSA: Gestione installazione e metratura</li>
                            <li>COLLEGAMENTO_PARTENZA: Connessioni lato partenza</li>
                            <li>COLLEGAMENTO_ARRIVO: Connessioni lato arrivo</li>
                            <li>CERTIFICAZIONE: Test e collaudi finali</li>
                        </ul>
                    </div>
                    <div class="functionality-image">
                        <img src="../images/moduli/comande-tipi.svg" alt="Gestione tipi di comande">
                    </div>
                </div>
            </div>
            
            <div class="module-section">
                <h2>Specifiche Tecniche</h2>
                <div class="specs-grid">
                    <div class="spec-card">
                        <h3>Database Schema</h3>
                        <p>Schema database ottimizzato con tabelle Comande e ComandaDettaglio per gestione efficiente di grandi volumi di lavori.</p>
                    </div>
                    <div class="spec-card">
                        <h3>API RESTful</h3>
                        <p>Endpoint API completi per integrazione con sistemi esterni e automazione dei processi di gestione comande.</p>
                    </div>
                    <div class="spec-card">
                        <h3>Workflow Engine</h3>
                        <p>Motore di workflow avanzato per gestione automatica delle transizioni di stato e controlli di business logic.</p>
                    </div>
                    <div class="spec-card">
                        <h3>Integrazione</h3>
                        <p>Integrazione completa con tutti i moduli CABLYS per un flusso di lavoro unificato e automatizzato.</p>
                    </div>
                </div>
            </div>
            
            <div class="module-section">
                <h2>Casi d'Uso</h2>
                <div class="case-studies">
                    <div class="case-study">
                        <h3>Gestione Cantiere Complesso</h3>
                        <p>Un cantiere con 500 cavi richiede coordinamento di team multipli per posa, collegamenti e certificazioni. Il modulo Comande permette di creare comande specifiche per ogni fase, assegnare i cavi ai team appropriati, monitorare l'avanzamento in tempo reale e identificare rapidamente eventuali ritardi o problemi.</p>
                    </div>
                    <div class="case-study">
                        <h3>Ottimizzazione Workflow</h3>
                        <p>Un'azienda vuole ottimizzare il workflow di installazione per ridurre i tempi morti. Utilizzando il modulo Comande, può analizzare le performance delle diverse fasi, identificare i colli di bottiglia e riorganizzare l'assegnazione dei lavori per massimizzare l'efficienza operativa.</p>
                    </div>
                </div>
            </div>
            
            <div class="module-cta">
                <h2>Pronto a Ottimizzare la Gestione delle Tue Comande?</h2>
                <p>Scopri come CABLYS può aiutarti a gestire i lavori in modo più efficiente e organizzato.</p>
                <a href="../index.html#contatti" class="btn-primary">Contattaci per una Demo</a>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="../images/logo.svg" alt="CABLYS Logo" class="logo-small">
                    <p>CABLYS - Cable Installation Advance System</p>
                </div>
                <div class="footer-links">
                    <h4>Collegamenti Rapidi</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="../index.html#servizi">Servizi</a></li>
                        <li><a href="../index.html#moduli">Moduli</a></li>
                        <li><a href="../index.html#chi-siamo">Chi Siamo</a></li>
                        <li><a href="../index.html#contatti">Contatti</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h4>Moduli</h4>
                    <ul>
                        <li><a href="cantieri.html">Gestione Cantieri</a></li>
                        <li><a href="cavi.html">Gestione Cavi</a></li>
                        <li><a href="parco-cavi.html">Parco Cavi</a></li>
                        <li><a href="certificazione.html">Certificazione</a></li>
                        <li><a href="comande.html">Comande</a></li>
                        <li><a href="report.html">Report</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h4>Contatti</h4>
                    <p>Email: <EMAIL></p>
                    <p>Telefono: +39 ************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 CABLYS - Cable Installation Advance System. Tutti i diritti riservati.</p>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
</body>
</html>
