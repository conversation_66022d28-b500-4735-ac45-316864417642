<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="ANALYZE_INJECTED_CODE" value="false" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b67d84d9-9a8c-42d6-9084-ff10a6a50c59" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/modules/utils.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2sgSzMKMwjttneJWzn2invnw3f1" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Node.js.CavoForm.js.executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-WindowsDefender&quot;: &quot;Microsoft Defender may affect IDE&quot;,
    &quot;Notification.DoNotAsk-WindowsDefender&quot;: &quot;true&quot;,
    &quot;Python tests.Python tests in test_importazione_bobine.py.executor&quot;: &quot;Run&quot;,
    &quot;Python tests.Python tests in test_sistema.py.executor&quot;: &quot;Run&quot;,
    &quot;Python.__init__.executor&quot;: &quot;Run&quot;,
    &quot;Python.analyze_duplicates.executor&quot;: &quot;Run&quot;,
    &quot;Python.cantieri_manager.executor&quot;: &quot;Run&quot;,
    &quot;Python.cavi.executor&quot;: &quot;Run&quot;,
    &quot;Python.database.executor&quot;: &quot;Run&quot;,
    &quot;Python.excel_manager.executor&quot;: &quot;Debug&quot;,
    &quot;Python.genera.executor&quot;: &quot;Run&quot;,
    &quot;Python.genera_file_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;Python.parco_cavi.executor&quot;: &quot;Run&quot;,
    &quot;Python.reports.executor&quot;: &quot;Run&quot;,
    &quot;Python.run_system.executor&quot;: &quot;Run&quot;,
    &quot;Python.run_system_simple.executor&quot;: &quot;Run&quot;,
    &quot;Python.run_system_updated.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_api.executor&quot;: &quot;Run&quot;,
    &quot;Python.utenti.executor&quot;: &quot;Run&quot;,
    &quot;Python.utils.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Shell Script.run_analysis.sh.executor&quot;: &quot;Run&quot;,
    &quot;disable.download.node.interpreter.editor.notification&quot;: &quot;true&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/CMS&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;aDefault&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Python.run_system_simple">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="CMS" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run_system_simple" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="CMS" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/webapp" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/webapp/run_system_simple.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run_analysis.sh" type="ShConfigurationType">
      <option name="SCRIPT_TEXT" value="" />
      <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
      <option name="SCRIPT_PATH" value="$PROJECT_DIR$/run_analysis.sh" />
      <option name="SCRIPT_OPTIONS" value="" />
      <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
      <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="INDEPENDENT_INTERPRETER_PATH" value="true" />
      <option name="INTERPRETER_PATH" value="powershell.exe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="EXECUTE_IN_TERMINAL" value="true" />
      <option name="EXECUTE_SCRIPT_FILE" value="true" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.159" />
        <option value="bundled-python-sdk-e0ed3721d81e-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b67d84d9-9a8c-42d6-9084-ff10a6a50c59" name="Changes" comment="" />
      <created>1738875985214</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1738875985214</updated>
      <workItem from="1745561368831" duration="468000" />
      <workItem from="1745949299045" duration="955000" />
      <workItem from="1745989001460" duration="504000" />
      <workItem from="1745990797981" duration="17115000" />
      <workItem from="1746098858369" duration="8232000" />
      <workItem from="1746119768141" duration="5774000" />
      <workItem from="1746250557850" duration="10025000" />
      <workItem from="1746273845935" duration="585000" />
      <workItem from="1746274514417" duration="8000000" />
      <workItem from="1746287089997" duration="1449000" />
      <workItem from="1746334729946" duration="7226000" />
      <workItem from="1746364039777" duration="5940000" />
      <workItem from="1746448681110" duration="20548000" />
      <workItem from="1746560813129" duration="2576000" />
      <workItem from="1746636580687" duration="9052000" />
      <workItem from="1746682608565" duration="80000" />
      <workItem from="1746682708388" duration="4282000" />
      <workItem from="1746807993449" duration="88000" />
      <workItem from="1746808142383" duration="5701000" />
      <workItem from="1746856085246" duration="1253000" />
      <workItem from="1746857404381" duration="389000" />
      <workItem from="1746858125384" duration="11955000" />
      <workItem from="1746914442792" duration="737000" />
      <workItem from="1746939866263" duration="8911000" />
      <workItem from="1746949021017" duration="1582000" />
      <workItem from="1746950644911" duration="580000" />
      <workItem from="1746951273989" duration="8360000" />
      <workItem from="1746966395325" duration="9978000" />
      <workItem from="1746993959677" duration="16032000" />
      <workItem from="1747137423208" duration="383000" />
      <workItem from="1747137843716" duration="2995000" />
      <workItem from="1747155528258" duration="7521000" />
      <workItem from="1747195947768" duration="281000" />
      <workItem from="1747196269556" duration="349000" />
      <workItem from="1747196762622" duration="278000" />
      <workItem from="1747197085166" duration="337000" />
      <workItem from="1747197476206" duration="3299000" />
      <workItem from="1747224235454" duration="9000" />
      <workItem from="1747224269398" duration="54000" />
      <workItem from="1747224341932" duration="7631000" />
      <workItem from="1747334252864" duration="3352000" />
      <workItem from="1747365950557" duration="1752000" />
      <workItem from="1747374866437" duration="2723000" />
      <workItem from="1747429928319" duration="4573000" />
      <workItem from="1747434554455" duration="3047000" />
      <workItem from="1747471853619" duration="13120000" />
      <workItem from="1747489618063" duration="4581000" />
      <workItem from="1747502234043" duration="7840000" />
      <workItem from="1747539286406" duration="466000" />
      <workItem from="1747540247543" duration="11363000" />
      <workItem from="1747554603236" duration="14902000" />
      <workItem from="1747720004413" duration="1430000" />
      <workItem from="1747760442779" duration="232000" />
      <workItem from="1747760734488" duration="320000" />
      <workItem from="1747761069249" duration="19000" />
      <workItem from="1747761102124" duration="414000" />
      <workItem from="1747763475586" duration="656000" />
      <workItem from="1747764145833" duration="1697000" />
      <workItem from="1747767650667" duration="468000" />
      <workItem from="1747769718725" duration="43000" />
      <workItem from="1747769839585" duration="388000" />
      <workItem from="1747773018447" duration="1637000" />
      <workItem from="1747806494345" duration="1193000" />
      <workItem from="1747888847292" duration="3113000" />
      <workItem from="1747933715720" duration="284000" />
      <workItem from="1747934012781" duration="442000" />
      <workItem from="1747934481158" duration="76000" />
      <workItem from="1747942059262" duration="3614000" />
      <workItem from="1747945722449" duration="56000" />
      <workItem from="1747945790731" duration="2457000" />
      <workItem from="1747970737409" duration="8406000" />
      <workItem from="1748030457555" duration="25000" />
      <workItem from="1748030503774" duration="336000" />
      <workItem from="1748030855813" duration="216000" />
      <workItem from="1748032177685" duration="84000" />
      <workItem from="1748032276320" duration="1662000" />
      <workItem from="1748034272572" duration="4802000" />
      <workItem from="1748062671451" duration="7364000" />
      <workItem from="1748070487185" duration="3240000" />
      <workItem from="1748097380742" duration="6883000" />
      <workItem from="1748152750816" duration="176000" />
      <workItem from="1748153012478" duration="949000" />
      <workItem from="1748161166825" duration="11560000" />
      <workItem from="1748199008437" duration="6917000" />
      <workItem from="1748238705937" duration="469000" />
      <workItem from="1748276803406" duration="5170000" />
      <workItem from="1748363199351" duration="258000" />
      <workItem from="1748363523842" duration="11959000" />
      <workItem from="1748403149057" duration="1612000" />
      <workItem from="1748412132060" duration="219000" />
      <workItem from="1748433523291" duration="418000" />
      <workItem from="1748433956503" duration="304000" />
      <workItem from="1748449639752" duration="1309000" />
      <workItem from="1748459666777" duration="33000" />
      <workItem from="1748459775372" duration="58000" />
      <workItem from="1748459848955" duration="216000" />
      <workItem from="1748460079592" duration="623000" />
      <workItem from="1748460744095" duration="700000" />
      <workItem from="1748464353175" duration="329000" />
      <workItem from="1748465601599" duration="1018000" />
      <workItem from="1748494401406" duration="2133000" />
      <workItem from="1748519059259" duration="159000" />
      <workItem from="1748519230274" duration="164000" />
      <workItem from="1748519452598" duration="181000" />
      <workItem from="1748519647650" duration="26000" />
      <workItem from="1748519685199" duration="125000" />
      <workItem from="1748605533899" duration="202000" />
      <workItem from="1748605758673" duration="278000" />
      <workItem from="1748606055287" duration="611000" />
      <workItem from="1748623902111" duration="1078000" />
      <workItem from="1748638015976" duration="624000" />
      <workItem from="1748638752110" duration="77000" />
      <workItem from="1748750428101" duration="30000" />
      <workItem from="1748877681240" duration="3288000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/webapp/backend/core/security.py</url>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/CMS$test_api.coverage" NAME="test_api Coverage Results" MODIFIED="1746272703401" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/webapp/backend" />
    <SUITE FILE_PATH="coverage/CMS$genera_file_test.coverage" NAME="genera_file_test Coverage Results" MODIFIED="1748363559691" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/CMS$parco_cavi.coverage" NAME="parco_cavi Coverage Results" MODIFIED="1746862281322" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/modules" />
    <SUITE FILE_PATH="coverage/CMS$main.coverage" NAME="main Coverage Results" MODIFIED="1746965186324" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/CMS$run_system_updated.coverage" NAME="run_system_updated Coverage Results" MODIFIED="1747429602287" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/webapp" />
    <SUITE FILE_PATH="coverage/CMS$run_system.coverage" NAME="run_system Coverage Results" MODIFIED="1747286964026" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/webapp" />
    <SUITE FILE_PATH="coverage/CMS$run_system_simple.coverage" NAME="run_system_simple Coverage Results" MODIFIED="1748622982207" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/webapp" />
  </component>
</project>